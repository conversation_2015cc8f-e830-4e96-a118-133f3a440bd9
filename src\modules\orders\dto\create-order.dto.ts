import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsUUID,
  IsArray,
  ValidateNested,
  IsNumber,
  IsPositive,
  IsString,
  IsOptional,
  IsObject,
} from 'class-validator';

export class CreateOrderItemDto {
  @ApiProperty({
    description: 'Product ID',
    example: '3cbd87ba-7a13-4abf-aafa-c72bb2b7eeb6',
  })
  @IsNotEmpty()
  @IsUUID()
  productId: string;

  @ApiProperty({
    description: 'Product variant ID (optional)',
    example: '3cbd87ba-7a13-4abf-aafa-c72bb2b7eeb6',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  productVariantId?: string;

  @ApiProperty({
    description: 'Quantity to order',
    example: 2,
    minimum: 1,
  })
  @IsNotEmpty()
  @IsNumber()
  @IsPositive()
  quantity: number;
}

export class AddressDto {
  @ApiProperty({
    description: 'Full name',
    example: '<PERSON>',
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Street address',
    example: '123 Main St, Apt 4B',
  })
  @IsNotEmpty()
  @IsString()
  street: string;

  @ApiProperty({
    description: 'City',
    example: 'New York',
  })
  @IsNotEmpty()
  @IsString()
  city: string;

  @ApiProperty({
    description: 'State or province',
    example: 'NY',
  })
  @IsNotEmpty()
  @IsString()
  state: string;

  @ApiProperty({
    description: 'ZIP or postal code',
    example: '10001',
  })
  @IsNotEmpty()
  @IsString()
  zipCode: string;

  @ApiProperty({
    description: 'Country',
    example: 'USA',
  })
  @IsNotEmpty()
  @IsString()
  country: string;

  @ApiProperty({
    description: 'Phone number',
    example: '******-123-4567',
    required: false,
  })
  @IsOptional()
  @IsString()
  phone?: string;
}

export class CreateOrderDto {
  @ApiProperty({
    description: 'Customer ID',
    example: '3cbd87ba-7a13-4abf-aafa-c72bb2b7eeb6',
  })
  @IsNotEmpty()
  @IsUUID()
  customerId: string;

  @ApiProperty({
    description: 'Array of order items',
    type: [CreateOrderItemDto],
  })
  @IsNotEmpty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateOrderItemDto)
  items: CreateOrderItemDto[];

  @ApiProperty({
    description: 'Shipping address',
    type: AddressDto,
  })
  @IsNotEmpty()
  @IsObject()
  @ValidateNested()
  @Type(() => AddressDto)
  shippingAddress: AddressDto;

  @ApiProperty({
    description: 'Billing address (if different from shipping)',
    type: AddressDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => AddressDto)
  billingAddress?: AddressDto;

  @ApiProperty({
    description: 'Order notes from customer',
    example: 'Please deliver after 5 PM',
    required: false,
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({
    description: 'Cross-tenant order ID for multi-vendor orders',
    example: '3cbd87ba-7a13-4abf-aafa-c72bb2b7eeb6',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  crossTenantOrderId?: string;
}
