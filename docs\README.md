# Multi-Tenant E-commerce API Documentation

Welcome to the documentation for the Multi-Tenant E-commerce API. This documentation provides comprehensive information about the API's architecture, features, and usage.

## Table of Contents

1. [API Documentation](./API_DOCUMENTATION.md)
2. [Technical Architecture](./ARCHITECTURE.md)
3. [Developer Guide](./DEVELOPER_GUIDE.md)
4. [Deployment Guide](./DEPLOYMENT_GUIDE.md)

## Overview

The Multi-Tenant E-commerce API is a robust backend solution built with NestJS, TypeScript, TypeORM, and PostgreSQL. It enables multiple vendors to operate their own e-commerce stores while sharing a common infrastructure. Each vendor gets their own dedicated database while maintaining a shared user base.

### Key Features

- **Multi-tenancy**: Database-per-tenant approach with subdomain-based tenant access
- **User Management**: Different roles (super-admin, vendor, customer) with appropriate permissions
- **Authentication**: JWT-based authentication with refresh tokens and security features
- **Product Management**: Categories, subcategories, and tenant-specific products
- **Order Management**: Order processing and tracking
- **Shared Catalog**: Product catalog accessible across all tenants

## Quick Start

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd multi-tenant-ecommerce-api
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Configure environment variables:
   ```bash
   cp .env.example .env
   ```

4. Create the main database:
   ```bash
   createdb tenant_main
   ```

5. Start the application:
   ```bash
   npm run start:dev
   ```

6. Access the Swagger documentation:
   ```
   http://localhost:3000/api/docs
   ```

## Documentation Structure

### [API Documentation](./API_DOCUMENTATION.md)

Detailed information about the API endpoints, request/response formats, and authentication mechanisms. This document serves as a reference for developers integrating with the API.

### [Technical Architecture](./ARCHITECTURE.md)

In-depth explanation of the system architecture, including the multi-tenancy implementation, database schema, module structure, and security considerations.

### [Developer Guide](./DEVELOPER_GUIDE.md)

Guidelines for developers who want to extend or modify the API. Includes information about project structure, adding new modules, working with multi-tenancy, and best practices.

### [Deployment Guide](./DEPLOYMENT_GUIDE.md)

Instructions for deploying the API to various environments, including local deployment, Docker deployment, and cloud deployment options. Also covers SSL configuration, monitoring, logging, and scaling considerations.

## API Endpoints Overview

### Authentication

- `POST /auth/register/super-admin` - Register a super admin
- `POST /auth/register/vendor` - Register a vendor
- `POST /auth/register/customer` - Register a customer
- `POST /auth/login` - Login for all user types
- `POST /auth/refresh` - Refresh access token
- `POST /auth/logout` - Logout user
- `GET /auth/profile` - Get current user profile

### Tenants

- `GET /tenants` - Get all tenants (super admin only)
- `GET /tenants/:id` - Get tenant by ID (super admin only)
- `POST /tenants` - Create a new tenant (vendor only)

### Users

- `GET /users` - Get all users (super admin only)
- `GET /users/:id` - Get user by ID (super admin, owner)
- `PATCH /users/:id` - Update user (super admin, owner)
- `DELETE /users/:id` - Delete user (super admin)

### Products (Planned)

- `GET /products` - Get all products
- `GET /products/:id` - Get product by ID
- `POST /products` - Create a product (vendor)
- `PATCH /products/:id` - Update product (vendor owner)
- `DELETE /products/:id` - Delete product (vendor owner)

### Categories (Planned)

- `GET /categories` - Get all categories
- `GET /categories/:id` - Get category by ID
- `POST /categories` - Create a category (super admin)
- `PATCH /categories/:id` - Update category (super admin)
- `DELETE /categories/:id` - Delete category (super admin)

### Orders (Planned)

- `GET /orders` - Get all orders (vendor own, customer own)
- `GET /orders/:id` - Get order by ID (vendor own, customer own)
- `POST /orders` - Create an order (customer)
- `PATCH /orders/:id` - Update order status (vendor own)

## Contributing

Please read the [Developer Guide](./DEVELOPER_GUIDE.md) for details on our code of conduct and the process for submitting pull requests.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
