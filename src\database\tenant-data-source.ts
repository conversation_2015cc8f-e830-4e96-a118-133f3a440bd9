import { DataSource, DataSourceOptions } from 'typeorm';
import * as dotenv from 'dotenv';

dotenv.config();

/**
 * Tenant Database Configuration
 *
 * This data source handles tenant-specific data:
 * - Products and inventory
 * - Orders and transactions
 * - Tenant-specific configurations
 *
 * Usage:
 * - TENANT_DB=<database_name> npm run migration:generate:tenant <MigrationName>
 * - TENANT_DB=<database_name> npm run migration:run:tenant
 * - TENANT_DB=<database_name> npm run migration:revert:tenant
 * - TENANT_DB=<database_name> npm run migration:show:tenant
 *
 * For multiple tenants:
 * - npm run migration:tenant:all
 */

/**
 * Gets the TypeORM configuration for a tenant database
 * @param databaseName The name of the tenant database
 * @returns DataSourceOptions configuration
 */
export const getTenantDataSourceConfig = (databaseName: string): DataSourceOptions => ({
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432', 10),
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  database: databaseName,
  entities: [
    // Tenant database entities only
    __dirname + '/../modules/products/entities/*.entity{.ts,.js}',
    __dirname + '/../modules/orders/entities/*.entity{.ts,.js}',
  ],
  migrations: [__dirname + '/migrations/tenant/*{.ts,.js}'],
  synchronize: false,
  logging: process.env.DB_LOGGING === 'true',
  migrationsTableName: 'tenant_migrations', // Unique table name for tenant DBs
});

/**
 * For TypeORM CLI usage with tenant databases.
 * Use TENANT_DB environment variable to specify which tenant database to target.
 */
const getTenantDatabaseName = (): string => {
  if (!process.env.TENANT_DB) {
    throw new Error(
      'TENANT_DB environment variable is required for tenant migrations.\n' +
      'Windows PowerShell: $env:TENANT_DB="<database_name>"; npm run migration:run:tenant\n' +
      'Linux/Mac Bash: TENANT_DB=<database_name> npm run migration:run:tenant\n' +
      'Example: $env:TENANT_DB="tenant_demo_one"; npm run migration:run:tenant'
    );
  }
  return process.env.TENANT_DB;
};

// Default export for TypeORM CLI - only create when TENANT_DB is set
// This prevents errors when importing this file for other purposes (like the migration runner)
let defaultDataSource: DataSource | undefined;

if (process.env.TENANT_DB) {
  defaultDataSource = new DataSource(getTenantDataSourceConfig(getTenantDatabaseName()));
}

export default defaultDataSource;