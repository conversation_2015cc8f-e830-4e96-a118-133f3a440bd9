import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Query,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { OrdersService } from './orders.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { Order } from './entities/order.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { UserRole } from '../../common/interfaces/user-roles.enum';
import { TenantRequest } from '../../common/interfaces/tenant-request.interface';

@ApiTags('Orders')
@Controller('orders')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class OrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  @Post()
  @Roles(UserRole.CUSTOMER)
  @UseGuards(RolesGuard)
  @ApiOperation({
    summary: 'Create a new order',
    description: 'Create a new order with items. Automatically calculates totals and validates stock.',
  })
  @ApiResponse({
    status: 201,
    description: 'Order created successfully',
    type: Order,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation failed or insufficient stock',
  })
  @ApiResponse({
    status: 404,
    description: 'Product not found',
  })
  async create(
    @Body() createOrderDto: CreateOrderDto,
    @Request() req: TenantRequest,
  ): Promise<Order> {
    return this.ordersService.create(
      createOrderDto,
      req.tenantSubdomain!,
      req.user?.vendor?.id || req.user?.id!,
    );
  }

  @Get()
  @ApiOperation({
    summary: 'Get all orders',
    description: 'Retrieve orders. Customers see only their orders, vendors see all orders for their tenant.',
  })
  @ApiResponse({
    status: 200,
    description: 'Orders retrieved successfully',
    type: [Order],
  })
  @ApiQuery({
    name: 'customerId',
    required: false,
    description: 'Filter by customer ID (vendor only)',
  })
  async findAll(
    @Request() req: TenantRequest,
    @Query('customerId') customerId?: string,
  ): Promise<Order[]> {
    // If user is a customer, only show their orders
    const filterCustomerId = req.user?.role === UserRole.CUSTOMER
      ? req.user.id
      : customerId;

    return this.ordersService.findAll(req.tenantSubdomain!, filterCustomerId);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get order by ID',
    description: 'Retrieve a specific order with all items.',
  })
  @ApiResponse({
    status: 200,
    description: 'Order retrieved successfully',
    type: Order,
  })
  @ApiResponse({
    status: 404,
    description: 'Order not found',
  })
  @ApiParam({ name: 'id', description: 'Order ID' })
  async findOne(
    @Param('id') id: string,
    @Request() req: TenantRequest,
  ): Promise<Order> {
    const order = await this.ordersService.findOne(id, req.tenantSubdomain!);

    // Customers can only view their own orders
    if (req.user?.role === UserRole.CUSTOMER && order.customerId !== req.user.id) {
      throw new Error('Access denied');
    }

    return order;
  }

  @Patch(':id')
  @Roles(UserRole.VENDOR, UserRole.SUPER_ADMIN)
  @UseGuards(RolesGuard)
  @ApiOperation({
    summary: 'Update order',
    description: 'Update order status, tracking information, or other details. Vendor/admin only.',
  })
  @ApiResponse({
    status: 200,
    description: 'Order updated successfully',
    type: Order,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid status transition',
  })
  @ApiResponse({
    status: 404,
    description: 'Order not found',
  })
  @ApiParam({ name: 'id', description: 'Order ID' })
  async update(
    @Param('id') id: string,
    @Body() updateOrderDto: UpdateOrderDto,
    @Request() req: TenantRequest,
  ): Promise<Order> {
    return this.ordersService.update(id, updateOrderDto, req.tenantSubdomain!);
  }

  @Delete(':id')
  @Roles(UserRole.VENDOR, UserRole.SUPER_ADMIN)
  @UseGuards(RolesGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Cancel order',
    description: 'Cancel an order and restore product stock. Cannot cancel shipped/delivered orders.',
  })
  @ApiResponse({
    status: 200,
    description: 'Order cancelled successfully',
    type: Order,
  })
  @ApiResponse({
    status: 400,
    description: 'Cannot cancel shipped or delivered orders',
  })
  @ApiResponse({
    status: 404,
    description: 'Order not found',
  })
  @ApiParam({ name: 'id', description: 'Order ID' })
  async cancel(
    @Param('id') id: string,
    @Request() req: TenantRequest,
  ): Promise<Order> {
    return this.ordersService.cancel(id, req.tenantSubdomain!);
  }
}
