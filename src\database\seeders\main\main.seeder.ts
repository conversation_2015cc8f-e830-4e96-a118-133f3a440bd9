import { Injectable } from '@nestjs/common';
import { UsersSeeder } from './users.seeder';
import { CategoriesSeeder } from './categories.seeder';
import { SubcategoriesSeeder } from './subcategories.seeder';

@Injectable()
export class MainSeeder {
  constructor(
    private readonly usersSeeder: UsersSeeder,
    private readonly categoriesSeeder: CategoriesSeeder,
    private readonly subcategoriesSeeder: SubcategoriesSeeder,
  ) {}

  async seed() {
    try {
      // Seed in order of dependencies
      await this.usersSeeder.seed();
      await this.categoriesSeeder.seed();
      await this.subcategoriesSeeder.seed();

      console.log('Main database seeding completed successfully');
    } catch (error) {
      console.error('Error during main database seeding:', error);
      throw error;
    }
  }
}
