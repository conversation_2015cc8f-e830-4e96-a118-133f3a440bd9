import { CacheModuleAsyncOptions } from '@nestjs/cache-manager';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { redisStore } from 'cache-manager-redis-yet';

export const cacheConfig: CacheModuleAsyncOptions = {
  imports: [ConfigModule],
  inject: [ConfigService],
  useFactory: async (configService: ConfigService) => {
    const redisUrl = configService.get<string>('REDIS_URL');

    // If Redis URL is not provided or Redis is not available, use memory store
    if (!redisUrl) {
      console.log('Redis not configured, using memory cache store');
      return {
        ttl: 300, // 5 minutes default TTL
        max: 1000, // Maximum number of items in cache
        isGlobal: true,
      };
    }

    try {
      return {
        store: redisStore,
        url: redisUrl,
        ttl: 300, // 5 minutes default TTL
        max: 1000, // Maximum number of items in cache
        isGlobal: true,
      };
    } catch (error) {
      console.log('Redis connection failed, falling back to memory cache:', error.message);
      return {
        ttl: 300, // 5 minutes default TTL
        max: 1000, // Maximum number of items in cache
        isGlobal: true,
      };
    }
  },
};
