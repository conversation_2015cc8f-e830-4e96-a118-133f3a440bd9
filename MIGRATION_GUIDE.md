# Migration Guide

This project uses a clean, independent migration system for main database and tenant databases.

## 🗄️ Database Architecture

- **Main Database**: Stores users, vendors, customers, and global categories
- **Tenant Databases**: Store tenant-specific data like products and orders

## 📋 Available Commands

### Main Database Migrations

```bash
# Generate a new migration
npm run migration:generate:main CreateUsersTable

# Run pending migrations
npm run migration:run:main

# Revert last migration
npm run migration:revert:main

# Show migration status
npm run migration:show:main
```

### Single Tenant Database Migrations

```bash
# Generate a new migration (requires TENANT_DB)
TENANT_DB=tenant_demo_one npm run migration:generate:tenant CreateProductsTable

# Run pending migrations for specific tenant
TENANT_DB=tenant_demo_one npm run migration:run:tenant

# Revert last migration for specific tenant
TENANT_DB=tenant_demo_one npm run migration:revert:tenant

# Show migration status for specific tenant
TENANT_DB=tenant_demo_one npm run migration:show:tenant
```

### All Tenant Databases Migrations

```bash
# Run migrations for all tenant databases
npm run migration:tenant:all
```

## 🔧 Platform-Specific Usage

### Windows (PowerShell)
```powershell
$env:TENANT_DB="tenant_demo_one"; npm run migration:run:tenant
```

### Linux/Mac (Bash)
```bash
TENANT_DB=tenant_demo_one npm run migration:run:tenant
```

## 📁 File Structure

```
src/database/
├── migrations/
│   ├── main/           # Main database migrations
│   └── tenant/         # Tenant database migrations
├── data-source.ts      # Main database configuration
├── tenant-data-source.ts           # Tenant database configuration
└── tenant-migration-runner.ts     # Multi-tenant migration utility
```

## 🎯 Best Practices

1. **Always specify TENANT_DB** when working with tenant migrations
2. **Test migrations** on a single tenant before running on all tenants
3. **Use descriptive names** for migration files
4. **Keep main and tenant migrations separate** - never mix entities between databases

## 🚨 Important Notes

- Main database uses `main_migrations` table
- Tenant databases use `tenant_migrations` table
- Each database type has completely independent migration tracking
- Always backup databases before running migrations in production

## 🔍 Troubleshooting

### Error: "TENANT_DB environment variable is required"
You must specify which tenant database to target:
```bash
TENANT_DB=tenant_demo_one npm run migration:run:tenant
```

### Error: "No pending migrations"
This means all migrations are already applied. Use `migration:show:*` to check status.

### Error: "Migration failed"
Check the error message and ensure:
- Database exists and is accessible
- Migration syntax is correct
- No conflicting schema changes
