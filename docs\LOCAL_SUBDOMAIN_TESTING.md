# Testing Subdomains Locally

This guide explains how to test subdomains locally in the multi-tenant e-commerce API.

## Prerequisites

1. Node.js and npm installed
2. PostgreSQL installed and running
3. The application set up and running

## Setting Up Local Subdomains

### 1. Modify Your Hosts File

To test subdomains locally, you need to add entries to your hosts file:

1. Open Notepad as Administrator
2. Open the hosts file located at `C:\Windows\System32\drivers\etc\hosts`
3. Add entries for your subdomains, for example:
   ```
   127.0.0.1    demo.localhost
   127.0.0.1    mystore.localhost
   127.0.0.1    anotherstore.localhost
   ```
4. Save the file

### 2. Register a Vendor

When you register a vendor, a tenant with a subdomain is automatically created.

#### Option 1: Using the Registration Script

We've provided a script to register a demo vendor:

```bash
# Install node-fetch if not already installed
npm install node-fetch

# Run the script
node scripts/register-demo-vendor.js
```

This will create a vendor with:
- Store name: Demo
- Subdomain: demo
- Email: <EMAIL>
- Password: Demo@123

#### Option 2: Using the API Directly

You can register a vendor using the API:

1. Make a POST request to `/auth/register/vendor`
2. Include the following JSON body:
   ```json
   {
     "email": "<EMAIL>",
     "password": "YourPassword123!",
     "passwordConfirmation": "YourPassword123!",
     "storeName": "Your Store Name"
   }
   ```

### 3. Access the Tenant Using the Subdomain

After registering a vendor, you can access the tenant using the subdomain:

```
http://demo.localhost:4000
```

Replace "demo" with your actual subdomain.

### 4. Verify Tenant Identification

To verify that the tenant is correctly identified, you can use the debug endpoint:

```
http://demo.localhost:4000/debug/tenant
```

This will show you:
- The identified tenant ID
- The host header
- The request URL
- The request method

### 5. Alternative: Using Query Parameters for Testing

In development mode, you can also use a query parameter to specify the tenant:

```
http://localhost:4000/debug/tenant?tenant=demo
```

This is useful for testing without modifying your hosts file.

## Troubleshooting

### Tenant Not Identified

If the tenant is not being identified:

1. Check your hosts file entries
2. Make sure you're using the correct subdomain format: `subdomain.localhost:4000`
3. Verify that the tenant exists in the database
4. Check the application logs for any errors

### Database Connection Issues

If you're having issues with the tenant database:

1. Make sure PostgreSQL is running
2. Check that the tenant database was created
3. Verify the database credentials in your `.env` file

### CORS Issues

If you're experiencing CORS issues:

1. Make sure the application is configured to allow requests from your subdomain
2. Check the browser console for CORS errors
3. Verify that the CORS configuration in `main.ts` is correct

## Additional Resources

- [Multi-Tenant Architecture Documentation](./ARCHITECTURE.md)
- [API Documentation](./API_DOCUMENTATION.md)
- [Developer Guide](./DEVELOPER_GUIDE.md)
