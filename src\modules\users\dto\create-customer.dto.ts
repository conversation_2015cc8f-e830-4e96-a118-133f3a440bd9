import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>otE<PERSON>y, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>th, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateCustomerDto {
  @ApiProperty({
    description: 'Customer email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'Customer password (must contain at least 1 uppercase letter, 1 lowercase letter, and 1 number or special character)',
    example: 'Password123!',
    minLength: 8,
  })
  @IsString()
  @MinLength(8)
  @Matches(/((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, {
    message: 'Password is too weak',
  })
  password: string;

  @ApiProperty({
    description: 'Password confirmation (must match password)',
    example: 'Password123!',
    minLength: 8,
  })
  @IsString()
  @MinLength(8)
  passwordConfirmation: string;
}
