import { Is<PERSON><PERSON>, IsNotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class LoginDto {
  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'User password',
    example: 'Demo@123',
    minLength: 8,
  })
  @IsString()
  @IsNotEmpty()
  password: string;
}
