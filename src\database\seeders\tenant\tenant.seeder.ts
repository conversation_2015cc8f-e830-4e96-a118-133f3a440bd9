import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { ProductsSeeder } from './products.seeder';

@Injectable()
export class TenantSeeder {
  constructor(
    private readonly dataSource: DataSource,
    private readonly productsSeeder: ProductsSeeder,
  ) {}

  async seed() {
    try {
      // Seed tenant-specific data in order of dependencies
      await this.productsSeeder.seed();

      console.log('Tenant database seeding completed successfully');
    } catch (error) {
      console.error('Error during tenant database seeding:', error);
      throw error;
    }
  }
}
