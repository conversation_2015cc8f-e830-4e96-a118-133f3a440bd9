import { Injectable, Logger, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { ProductCatalog } from '../entities/product-catalog.entity';
import { Product } from '../../products/entities/product.entity';
import { Vendor } from '../../users/entities/vendor.entity';
import { TenantsService } from '../../tenants/tenants.service';


@Injectable()
export class CatalogSyncService {
  private readonly logger = new Logger(CatalogSyncService.name);

  constructor(
    @InjectRepository(ProductCatalog)
    private catalogRepository: Repository<ProductCatalog>,
    private tenantsService: TenantsService,
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,
  ) {}

  /**
   * Sync all products from all active tenants to the catalog
   * This is used for initial data migration and periodic full sync
   */
  async syncAllProducts(): Promise<{ success: number; failed: number; total: number }> {
    this.logger.log('Starting full catalog sync for all tenants');

    const vendors = await this.tenantsService.findAll();
    let successCount = 0;
    let failedCount = 0;
    let totalCount = 0;

    for (const vendor of vendors) {
      if (!vendor.isActive || !vendor.databaseName) {
        this.logger.warn(`Skipping inactive vendor: ${vendor.subdomain}`);
        continue;
      }

      try {
        const result = await this.syncVendorProducts(vendor);
        successCount += result.success;
        failedCount += result.failed;
        totalCount += result.total;
      } catch (error) {
        this.logger.error(`Failed to sync vendor ${vendor.subdomain}:`, error);
        failedCount++;
      }
    }

    // Clear all cache after full sync
    await this.invalidateAllCache();

    this.logger.log(`Full sync completed: ${successCount} success, ${failedCount} failed, ${totalCount} total`);

    return { success: successCount, failed: failedCount, total: totalCount };
  }

  /**
   * Sync products for a specific vendor
   */
  async syncVendorProducts(vendor: Vendor): Promise<{ success: number; failed: number; total: number }> {
    this.logger.log(`Syncing products for vendor: ${vendor.subdomain}`);

    try {
      const tenantConnection = await this.tenantsService.getTenantConnection(vendor.subdomain);
      const productRepo = tenantConnection.dataSource.getRepository(Product);

      const products = await productRepo.find({
        order: { updatedAt: 'DESC' }
      });

      let successCount = 0;
      let failedCount = 0;

      for (const product of products) {
        try {
          await this.upsertCatalogProduct(product, vendor);
          successCount++;
        } catch (error) {
          this.logger.error(`Failed to sync product ${product.id} for vendor ${vendor.subdomain}:`, error);
          failedCount++;
        }
      }

      this.logger.log(`Vendor ${vendor.subdomain} sync completed: ${successCount} success, ${failedCount} failed`);

      return { success: successCount, failed: failedCount, total: products.length };
    } catch (error) {
      this.logger.error(`Failed to connect to tenant database for ${vendor.subdomain}:`, error);
      throw error;
    }
  }

  /**
   * Upsert a single product to the catalog
   * This is used for real-time sync when products are created/updated
   */
  async upsertCatalogProduct(product: Product, vendor: Vendor): Promise<ProductCatalog> {
    try {
      const existingCatalogProduct = await this.catalogRepository.findOne({
        where: {
          tenantSubdomain: vendor.subdomain,
          tenantProductId: product.id
        }
      });

      const catalogData = this.mapProductToCatalog(product, vendor);

      let catalogProduct: ProductCatalog;

      if (existingCatalogProduct) {
        await this.catalogRepository.update(existingCatalogProduct.id, catalogData);
        const updatedProduct = await this.catalogRepository.findOne({
          where: { id: existingCatalogProduct.id }
        });
        if (!updatedProduct) {
          throw new Error(`Failed to find updated catalog product ${existingCatalogProduct.id}`);
        }
        catalogProduct = updatedProduct;
      } else {
        catalogProduct = await this.catalogRepository.save(catalogData);
      }

      // Invalidate related cache entries
      await this.invalidateProductCache(vendor.id, product.categoryId);

      this.logger.debug(`Synced product ${product.id} to catalog for vendor ${vendor.subdomain}`);

      return catalogProduct;
    } catch (error) {
      this.logger.error(`Failed to upsert catalog product ${product.id}:`, error);
      throw error;
    }
  }

  /**
   * Remove a product from the catalog
   * This is used when products are deleted from tenant databases
   */
  async removeFromCatalog(tenantProductId: string, tenantSubdomain: string): Promise<void> {
    try {
      const result = await this.catalogRepository.delete({
        tenantProductId,
        tenantSubdomain
      });

      if (result.affected && result.affected > 0) {
        this.logger.debug(`Removed product ${tenantProductId} from catalog for tenant ${tenantSubdomain}`);
        await this.invalidateAllCache();
      }
    } catch (error) {
      this.logger.error(`Failed to remove product ${tenantProductId} from catalog:`, error);
      throw error;
    }
  }

  /**
   * Update product status in catalog (e.g., when product is deactivated)
   */
  async updateProductStatus(tenantProductId: string, tenantSubdomain: string, isActive: boolean): Promise<void> {
    try {
      const result = await this.catalogRepository.update(
        { tenantProductId, tenantSubdomain },
        { isActive, syncedAt: new Date() }
      );

      if (result.affected && result.affected > 0) {
        this.logger.debug(`Updated product ${tenantProductId} status to ${isActive} in catalog`);
        await this.invalidateAllCache();
      }
    } catch (error) {
      this.logger.error(`Failed to update product ${tenantProductId} status in catalog:`, error);
      throw error;
    }
  }

  /**
   * Map Product entity to ProductCatalog data
   */
  private mapProductToCatalog(product: Product, vendor: Vendor): Partial<ProductCatalog> {
    // Create search vector from name and description
    const searchText = `${product.name} ${product.description || ''}`.trim();

    // Determine stock status
    let stockStatus = 'out_of_stock';
    if (product.stock > 10) {
      stockStatus = 'in_stock';
    } else if (product.stock > 0) {
      stockStatus = 'low_stock';
    }

    return {
      tenantProductId: product.id,
      vendorId: vendor.id,
      tenantSubdomain: vendor.subdomain,
      name: product.name,
      description: product.description,
      price: product.price,
      stock: product.stock,
      images: product.images || [],
      categoryId: product.categoryId,
      isActive: product.isActive,
      searchVector: searchText,
      stockStatus,
      syncedAt: new Date()
    };
  }

  /**
   * Invalidate cache for specific product-related entries
   */
  private async invalidateProductCache(vendorId?: string, categoryId?: string): Promise<void> {
    const patterns = ['products:catalog:*', 'search:*'];

    if (vendorId) {
      patterns.push(`products:vendor:${vendorId}:*`);
    }

    if (categoryId) {
      patterns.push(`products:category:${categoryId}:*`);
    }

    for (const pattern of patterns) {
      await this.cacheManager.del(pattern);
    }
  }

  /**
   * Invalidate all cache entries
   */
  private async invalidateAllCache(): Promise<void> {
    await this.cacheManager.del('products:*');
    await this.cacheManager.del('search:*');
  }

  /**
   * Get sync statistics
   */
  async getSyncStats(): Promise<{
    totalProducts: number;
    lastSyncTime: Date;
    vendorCounts: { vendorId: string; subdomain: string; productCount: number }[];
  }> {
    const totalProducts = await this.catalogRepository.count();

    const lastSyncResult = await this.catalogRepository
      .createQueryBuilder('pc')
      .select('MAX(pc.syncedAt)', 'lastSync')
      .getRawOne();

    const vendorCounts = await this.catalogRepository
      .createQueryBuilder('pc')
      .select('pc.vendorId', 'vendorId')
      .addSelect('pc.tenantSubdomain', 'subdomain')
      .addSelect('COUNT(*)', 'productCount')
      .groupBy('pc.vendorId')
      .addGroupBy('pc.tenantSubdomain')
      .getRawMany();

    return {
      totalProducts,
      lastSyncTime: lastSyncResult?.lastSync || new Date(),
      vendorCounts: vendorCounts.map(vc => ({
        vendorId: vc.vendorId,
        subdomain: vc.subdomain,
        productCount: parseInt(vc.productCount)
      }))
    };
  }
}
