import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CacheModule } from '@nestjs/cache-manager';
import { CqrsModule } from '@nestjs/cqrs';
import { ProductsService } from './products.service';
import { ProductsEnhancedService } from './services/products-enhanced.service';
import { ProductsController } from './products.controller';
import { ProductsEnhancedController } from './controllers/products-enhanced.controller';
import { TenantsModule } from '../tenants/tenants.module';
import { CatalogModule } from '../catalog/catalog.module';
import { Product } from './entities/product.entity';
import { CategoriesModule } from '../categories/categories.module';

@Module({
  imports: [
    TenantsModule,
    CategoriesModule,
    CatalogModule,
    CacheModule.register(),
    CqrsModule,
    TypeOrmModule.forFeature([Product])
  ],
  controllers: [ProductsController, ProductsEnhancedController],
  providers: [
    ProductsService,
    ProductsEnhancedService,
  ],
  exports: [
    ProductsService,
    ProductsEnhancedService,
  ],
})
export class ProductsModule {}
