# Multi-Tenant E-commerce API: Developer Guide

This guide provides instructions for developers who want to extend or modify the Multi-Tenant E-commerce API.

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)
- PostgreSQL (v12 or higher)
- Git

### Setup Development Environment

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd multi-tenant-ecommerce-api
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Configure environment variables:
   ```bash
   cp .env.example .env
   ```
   Edit the `.env` file with your local configuration.

4. Create the main database:
   ```bash
   createdb tenant_main
   ```

5. Start the development server:
   ```bash
   npm run start:dev
   ```

6. Access the Swagger documentation:
   ```
   http://localhost:3000/api/docs
   ```

## Project Structure

```
src/
├── common/                 # Shared code
│   ├── decorators/         # Custom decorators
│   ├── guards/             # Authorization guards
│   ├── interfaces/         # TypeScript interfaces
│   ├── middleware/         # HTTP middleware
│   └── interceptors/       # NestJS interceptors
├── database/               # Database configuration
│   └── migrations/         # TypeORM migrations
├── modules/                # Feature modules
│   ├── auth/               # Authentication module
│   ├── categories/         # Categories module
│   ├── orders/             # Orders module
│   ├── products/           # Products module
│   ├── tenants/            # Tenants module
│   └── users/              # Users module
├── app.controller.ts       # Main app controller
├── app.module.ts           # Main app module
├── app.service.ts          # Main app service
└── main.ts                 # Application entry point
```

## Adding a New Module

To add a new feature module to the API, follow these steps:

1. Generate a new module using the NestJS CLI:
   ```bash
   nest generate module modules/your-module
   ```

2. Generate the necessary components:
   ```bash
   nest generate controller modules/your-module
   nest generate service modules/your-module
   nest generate class modules/your-module/entities/your-entity
   nest generate class modules/your-module/dto/create-your-entity.dto
   nest generate class modules/your-module/dto/update-your-entity.dto
   ```

3. Define your entity schema in `your-entity.ts`:
   ```typescript
   import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';

   @Entity('your_entities')
   export class YourEntity {
     @PrimaryGeneratedColumn('uuid')
     id: string;

     @Column()
     name: string;

     // Add more columns as needed

     @CreateDateColumn()
     createdAt: Date;

     @UpdateDateColumn()
     updatedAt: Date;
   }
   ```

4. Implement your controller with proper decorators:
   ```typescript
   import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards } from '@nestjs/common';
   import { YourService } from './your.service';
   import { CreateYourEntityDto } from './dto/create-your-entity.dto';
   import { UpdateYourEntityDto } from './dto/update-your-entity.dto';
   import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
   import { RolesGuard } from '../../common/guards/roles.guard';
   import { Roles } from '../../common/decorators/roles.decorator';
   import { UserRole } from '../../common/interfaces/user-roles.enum';
   import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

   @ApiTags('your-entities')
   @Controller('your-entities')
   export class YourController {
     constructor(private readonly yourService: YourService) {}

     @Post()
     @UseGuards(JwtAuthGuard, RolesGuard)
     @Roles(UserRole.VENDOR)
     @ApiBearerAuth('JWT-auth')
     @ApiOperation({ summary: 'Create a new entity' })
     create(@Body() createYourEntityDto: CreateYourEntityDto) {
       return this.yourService.create(createYourEntityDto);
     }

     // Implement other CRUD operations
   }
   ```

5. Implement your service with proper error handling:
   ```typescript
   import { Injectable, NotFoundException } from '@nestjs/common';
   import { InjectRepository } from '@nestjs/typeorm';
   import { Repository } from 'typeorm';
   import { YourEntity } from './entities/your-entity';
   import { CreateYourEntityDto } from './dto/create-your-entity.dto';
   import { UpdateYourEntityDto } from './dto/update-your-entity.dto';

   @Injectable()
   export class YourService {
     constructor(
       @InjectRepository(YourEntity)
       private yourRepository: Repository<YourEntity>,
     ) {}

     async create(createYourEntityDto: CreateYourEntityDto): Promise<YourEntity> {
       const entity = this.yourRepository.create(createYourEntityDto);
       return this.yourRepository.save(entity);
     }

     // Implement other CRUD operations
   }
   ```

6. Import your module in the app module or another parent module:
   ```typescript
   import { YourModule } from './modules/your-module/your.module';

   @Module({
     imports: [
       // Other imports
       YourModule,
     ],
   })
   export class AppModule {}
   ```

## Working with Multi-Tenancy

When adding features that need to be tenant-aware, follow these guidelines:

### For Tenant-Specific Entities

1. Create your entity in the appropriate module
2. In your service, inject the `TenantsService`:
   ```typescript
   constructor(
     private tenantsService: TenantsService,
   ) {}
   ```

3. Use the tenant connection for database operations:
   ```typescript
   async findAll(tenantId: string): Promise<YourEntity[]> {
     const tenantConnection = await this.tenantsService.getTenantConnection(tenantId);
     const repository = tenantConnection.dataSource.getRepository(YourEntity);
     return repository.find();
   }
   ```

4. In your controller, extract the tenant ID from the request:
   ```typescript
   @Get()
   async findAll(@Req() req: TenantRequest) {
     return this.yourService.findAll(req.tenantId);
   }
   ```

### For Shared Entities

1. Create your entity in the appropriate module
2. Use the main database connection:
   ```typescript
   @Injectable()
   export class YourService {
     constructor(
       @InjectRepository(YourEntity)
       private yourRepository: Repository<YourEntity>,
     ) {}

     // Implement CRUD operations using the main connection
   }
   ```

## Authentication and Authorization

### Adding Role-Based Access Control

1. Use the `@Roles` decorator to specify which roles can access an endpoint:
   ```typescript
   @Post()
   @UseGuards(JwtAuthGuard, RolesGuard)
   @Roles(UserRole.SUPER_ADMIN)
   create(@Body() createDto: CreateDto) {
     return this.service.create(createDto);
   }
   ```

2. For multiple roles:
   ```typescript
   @Roles(UserRole.SUPER_ADMIN, UserRole.VENDOR)
   ```

### Accessing the Current User

Use the `@CurrentUser()` decorator to access the authenticated user:
```typescript
@Get('profile')
@UseGuards(JwtAuthGuard)
getProfile(@CurrentUser() user: { id: string; email: string; role: string }) {
  return user;
}
```

## Database Migrations

### Creating a Migration

1. Make changes to your entity classes
2. Generate a migration:
   ```bash
   npm run migration:generate -- -n YourMigrationName
   ```

3. Run the migration:
   ```bash
   npm run migration:run
   ```

### Reverting a Migration

```bash
npm run migration:revert
```

## Testing

### Unit Tests

1. Create test files with `.spec.ts` extension
2. Run tests:
   ```bash
   npm run test
   ```

### E2E Tests

1. Create test files in the `test` directory with `.e2e-spec.ts` extension
2. Run E2E tests:
   ```bash
   npm run test:e2e
   ```

## API Documentation

The API uses Swagger for documentation. To document your endpoints:

1. Use Swagger decorators in your controllers:
   ```typescript
   @ApiTags('your-tag')
   @ApiOperation({ summary: 'Operation summary' })
   @ApiResponse({ status: 200, description: 'Success response' })
   @ApiResponse({ status: 401, description: 'Unauthorized' })
   ```

2. Document DTOs with `@ApiProperty()`:
   ```typescript
   export class CreateUserDto {
     @ApiProperty({ example: '<EMAIL>', description: 'User email' })
     @IsEmail()
     email: string;
   }
   ```

3. Access the Swagger UI at `/api/docs`

## Best Practices

1. **Error Handling**: Use NestJS exceptions (`NotFoundException`, `BadRequestException`, etc.)
2. **Validation**: Use class-validator decorators in DTOs
3. **Security**: Always use guards for protected endpoints
4. **Tenant Awareness**: Always consider multi-tenancy when designing new features
5. **Type Safety**: Leverage TypeScript's type system for better code quality
