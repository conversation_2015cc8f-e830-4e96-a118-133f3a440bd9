# Implementation Services & Architecture Patterns

## 🏗️ **Combined Best Implementation Strategy**

This document outlines the NestJS services and patterns that implement the combined optimal schema.

## 📦 **Core Service Architecture**

### **1. Product Catalog Sync Service** (Best from All Agents)
```typescript
// src/modules/catalog/catalog-sync.service.ts
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Cache } from 'cache-manager';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ProductCatalog } from './entities/product-catalog.entity';
import { Product } from '../products/entities/product.entity';
import { TenantsService } from '../tenants/tenants.service';

@Injectable()
export class CatalogSyncService {
  private readonly logger = new Logger(CatalogSyncService.name);

  constructor(
    @InjectRepository(ProductCatalog)
    private catalogRepository: Repository<ProductCatalog>,
    private tenantsService: TenantsService,
    private cacheManager: Cache,
    private eventEmitter: EventEmitter2,
  ) {}

  /**
   * Sync all products from all active tenants
   * Called by scheduled job or manual trigger
   */
  async syncAllProducts(): Promise<void> {
    const startTime = Date.now();
    this.logger.log('Starting full product catalog sync');

    try {
      const tenants = await this.tenantsService.findAllActive();
      let totalSynced = 0;

      for (const tenant of tenants) {
        const synced = await this.syncTenantProducts(tenant);
        totalSynced += synced;
      }

      const duration = Date.now() - startTime;
      this.logger.log(`Sync completed: ${totalSynced} products in ${duration}ms`);

      // Clear all product caches
      await this.clearProductCaches();

    } catch (error) {
      this.logger.error('Full sync failed:', error);
      throw error;
    }
  }

  /**
   * Sync products from a specific tenant
   */
  async syncTenantProducts(tenant: Tenant): Promise<number> {
    try {
      const tenantConnection = await this.tenantsService.getTenantConnection(tenant.subdomain);
      const productRepo = tenantConnection.dataSource.getRepository(Product);

      // Get all active products from tenant
      const products = await productRepo.find({
        where: { status: 'active', is_published: true },
        order: { updated_at: 'DESC' }
      });

      let syncedCount = 0;
      for (const product of products) {
        await this.upsertCatalogProduct(product, tenant);
        syncedCount++;
      }

      // Update sync status
      await this.updateSyncStatus(tenant.id, 'products', 'success', syncedCount);

      return syncedCount;

    } catch (error) {
      this.logger.error(`Sync failed for tenant ${tenant.subdomain}:`, error);
      await this.updateSyncStatus(tenant.id, 'products', 'failed', 0, error.message);
      return 0;
    }
  }

  /**
   * Sync single product (real-time sync)
   */
  async syncSingleProduct(product: Product, tenant: Tenant): Promise<void> {
    try {
      await this.upsertCatalogProduct(product, tenant);

      // Emit event for real-time updates
      this.eventEmitter.emit('product.synced', {
        productId: product.id,
        tenantId: tenant.id,
        action: 'upsert'
      });

      // Clear related caches
      await this.clearProductCaches([
        `products:all`,
        `products:tenant:${tenant.id}`,
        `products:category:${product.category_id}`,
        `search:*` // Clear all search caches
      ]);

    } catch (error) {
      this.logger.error(`Single product sync failed:`, error);
      throw error;
    }
  }

  /**
   * Remove product from catalog (when deleted from tenant)
   */
  async removeFromCatalog(productId: string, tenantId: string): Promise<void> {
    await this.catalogRepository.delete({
      tenant_id: tenantId,
      tenant_product_id: productId
    });

    // Clear caches
    await this.clearProductCaches();

    this.eventEmitter.emit('product.synced', {
      productId,
      tenantId,
      action: 'delete'
    });
  }

  /**
   * Upsert product in catalog
   */
  private async upsertCatalogProduct(product: Product, tenant: Tenant): Promise<void> {
    // Check if catalog entry exists
    const existingEntry = await this.catalogRepository.findOne({
      where: {
        tenant_id: tenant.id,
        tenant_product_id: product.id
      }
    });

    // Prepare catalog data
    const catalogData = {
      tenant_id: tenant.id,
      tenant_product_id: product.id,
      name: product.name,
      slug: product.slug,
      description: product.description,
      short_description: product.short_description,
      price: product.price,
      compare_price: product.compare_price,
      category_id: product.category_id,
      category_path: await this.buildCategoryPath(product.category_id),
      brand: product.brand,
      sku: product.sku,
      stock_quantity: product.stock_quantity,
      stock_status: this.determineStockStatus(product.stock_quantity, product.low_stock_threshold),
      images: product.images,
      attributes: product.attributes,
      tags: product.tags,
      featured: product.featured,
      status: product.status,
      is_published: product.is_published,
      // search_vector will be auto-generated by trigger
      last_synced_at: new Date()
    };

    if (existingEntry) {
      await this.catalogRepository.update(existingEntry.id, catalogData);
    } else {
      await this.catalogRepository.save(catalogData);
    }
  }

  /**
   * Build category path for fast filtering
   */
  private async buildCategoryPath(categoryId: string): Promise<string> {
    // This would build a path like "Electronics > Smartphones > Android"
    // Implementation depends on your category hierarchy logic
    return 'Electronics > Smartphones'; // Simplified
  }

  /**
   * Determine stock status based on quantity
   */
  private determineStockStatus(quantity: number, lowThreshold: number = 5): string {
    if (quantity <= 0) return 'out_of_stock';
    if (quantity <= lowThreshold) return 'low_stock';
    return 'in_stock';
  }

  /**
   * Clear product-related caches
   */
  private async clearProductCaches(patterns: string[] = ['products:*', 'search:*']): Promise<void> {
    for (const pattern of patterns) {
      if (pattern.includes('*')) {
        // Clear pattern-based caches (implementation depends on cache store)
        await this.cacheManager.reset(); // Simplified - use pattern matching in production
      } else {
        await this.cacheManager.del(pattern);
      }
    }
  }

  /**
   * Update sync status tracking
   */
  private async updateSyncStatus(
    tenantId: string,
    entityType: string,
    status: string,
    recordsSynced: number,
    errorMessage?: string
  ): Promise<void> {
    // Implementation for sync_status table updates
    // This helps monitor sync health across tenants
  }
}
```

### **2. Enhanced Products Service** (Performance Optimized)
```typescript
// src/modules/products/products-enhanced.service.ts
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { Cache } from 'cache-manager';
import { ProductCatalog } from '../catalog/entities/product-catalog.entity';

export interface ProductFilters {
  category?: string;
  subcategory?: string;
  tenant?: string;
  minPrice?: number;
  maxPrice?: number;
  brand?: string;
  tags?: string[];
  search?: string;
  featured?: boolean;
  inStock?: boolean;
  sortBy?: 'price' | 'name' | 'created_at' | 'rating';
  sortOrder?: 'ASC' | 'DESC';
}

export interface PaginationOptions {
  page: number;
  limit: number;
}

export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

@Injectable()
export class ProductsEnhancedService {
  constructor(
    @InjectRepository(ProductCatalog)
    private catalogRepository: Repository<ProductCatalog>,
    private cacheManager: Cache,
  ) {}

  /**
   * Fast product listing using catalog table
   */
  async findAllFromCatalog(
    filters: ProductFilters = {},
    pagination: PaginationOptions = { page: 1, limit: 20 }
  ): Promise<PaginatedResult<ProductCatalog>> {

    // Generate cache key
    const cacheKey = `products:${JSON.stringify({ filters, pagination })}`;

    // Try cache first
    const cached = await this.cacheManager.get<PaginatedResult<ProductCatalog>>(cacheKey);
    if (cached) {
      return cached;
    }

    // Build query
    const queryBuilder = this.buildProductQuery(filters);

    // Apply pagination
    const offset = (pagination.page - 1) * pagination.limit;
    queryBuilder.skip(offset).take(pagination.limit);

    // Execute query
    const [products, total] = await queryBuilder.getManyAndCount();

    // Build result
    const result: PaginatedResult<ProductCatalog> = {
      data: products,
      total,
      page: pagination.page,
      limit: pagination.limit,
      totalPages: Math.ceil(total / pagination.limit),
      hasNext: pagination.page < Math.ceil(total / pagination.limit),
      hasPrev: pagination.page > 1
    };

    // Cache for 10 minutes
    await this.cacheManager.set(cacheKey, result, 600);

    return result;
  }

  /**
   * Advanced search with full-text search
   */
  async searchProducts(
    query: string,
    filters: ProductFilters = {},
    pagination: PaginationOptions = { page: 1, limit: 20 }
  ): Promise<PaginatedResult<ProductCatalog>> {

    const cacheKey = `search:${query}:${JSON.stringify({ filters, pagination })}`;

    const cached = await this.cacheManager.get<PaginatedResult<ProductCatalog>>(cacheKey);
    if (cached) return cached;

    const queryBuilder = this.catalogRepository
      .createQueryBuilder('pc')
      .leftJoinAndSelect('categories', 'c', 'pc.category_id = c.id')
      .leftJoinAndSelect('tenants', 't', 'pc.tenant_id = t.id')
      .where('pc.status = :status', { status: 'active' })
      .andWhere('pc.is_published = :published', { published: true });

    // Full-text search
    if (query) {
      queryBuilder.andWhere(
        'pc.search_vector @@ plainto_tsquery(:query)',
        { query }
      );
      // Order by relevance
      queryBuilder.orderBy('ts_rank(pc.search_vector, plainto_tsquery(:query))', 'DESC');
    }

    // Apply additional filters
    this.applyFilters(queryBuilder, filters);

    // Pagination
    const offset = (pagination.page - 1) * pagination.limit;
    queryBuilder.skip(offset).take(pagination.limit);

    const [products, total] = await queryBuilder.getManyAndCount();

    const result: PaginatedResult<ProductCatalog> = {
      data: products,
      total,
      page: pagination.page,
      limit: pagination.limit,
      totalPages: Math.ceil(total / pagination.limit),
      hasNext: pagination.page < Math.ceil(total / pagination.limit),
      hasPrev: pagination.page > 1
    };

    // Cache search results for 5 minutes
    await this.cacheManager.set(cacheKey, result, 300);

    return result;
  }

  /**
   * Build base product query with filters
   */
  private buildProductQuery(filters: ProductFilters): SelectQueryBuilder<ProductCatalog> {
    const queryBuilder = this.catalogRepository
      .createQueryBuilder('pc')
      .leftJoinAndSelect('categories', 'c', 'pc.category_id = c.id')
      .leftJoinAndSelect('tenants', 't', 'pc.tenant_id = t.id')
      .where('pc.status = :status', { status: 'active' })
      .andWhere('pc.is_published = :published', { published: true });

    this.applyFilters(queryBuilder, filters);
    this.applySorting(queryBuilder, filters);

    return queryBuilder;
  }

  /**
   * Apply filters to query builder
   */
  private applyFilters(queryBuilder: SelectQueryBuilder<ProductCatalog>, filters: ProductFilters): void {
    if (filters.category) {
      queryBuilder.andWhere('pc.category_id = :categoryId', { categoryId: filters.category });
    }

    if (filters.tenant) {
      queryBuilder.andWhere('pc.tenant_id = :tenantId', { tenantId: filters.tenant });
    }

    if (filters.minPrice) {
      queryBuilder.andWhere('pc.price >= :minPrice', { minPrice: filters.minPrice });
    }

    if (filters.maxPrice) {
      queryBuilder.andWhere('pc.price <= :maxPrice', { maxPrice: filters.maxPrice });
    }

    if (filters.brand) {
      queryBuilder.andWhere('pc.brand = :brand', { brand: filters.brand });
    }

    if (filters.featured) {
      queryBuilder.andWhere('pc.featured = :featured', { featured: filters.featured });
    }

    if (filters.inStock) {
      queryBuilder.andWhere('pc.stock_status != :outOfStock', { outOfStock: 'out_of_stock' });
    }

    if (filters.tags && filters.tags.length > 0) {
      queryBuilder.andWhere('pc.tags && :tags', { tags: filters.tags });
    }
  }

  /**
   * Apply sorting to query builder
   */
  private applySorting(queryBuilder: SelectQueryBuilder<ProductCatalog>, filters: ProductFilters): void {
    const sortBy = filters.sortBy || 'created_at';
    const sortOrder = filters.sortOrder || 'DESC';

    switch (sortBy) {
      case 'price':
        queryBuilder.orderBy('pc.price', sortOrder);
        break;
      case 'name':
        queryBuilder.orderBy('pc.name', sortOrder);
        break;
      case 'rating':
        queryBuilder.orderBy('pc.average_rating', sortOrder);
        break;
      default:
        queryBuilder.orderBy('pc.created_at', sortOrder);
    }
  }
}
```

### **3. Cross-Tenant Customer Service** (Claude's Innovation)
```typescript
// src/modules/customers/cross-tenant-customer.service.ts
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Customer } from './entities/customer.entity';
import { CustomerTenantProfile } from './entities/customer-tenant-profile.entity';
import { CrossTenantOrder } from '../orders/entities/cross-tenant-order.entity';

@Injectable()
export class CrossTenantCustomerService {
  constructor(
    @InjectRepository(Customer)
    private customerRepository: Repository<Customer>,
    @InjectRepository(CustomerTenantProfile)
    private profileRepository: Repository<CustomerTenantProfile>,
    @InjectRepository(CrossTenantOrder)
    private crossOrderRepository: Repository<CrossTenantOrder>,
  ) {}

  /**
   * Get customer with all tenant profiles
   */
  async getCustomerWithProfiles(customerId: string): Promise<Customer & { tenantProfiles: CustomerTenantProfile[] }> {
    const customer = await this.customerRepository.findOne({
      where: { id: customerId },
      relations: ['addresses']
    });

    if (!customer) {
      throw new NotFoundException('Customer not found');
    }

    const tenantProfiles = await this.profileRepository.find({
      where: { customer_id: customerId },
      relations: ['tenant']
    });

    return { ...customer, tenantProfiles };
  }

  /**
   * Get or create customer tenant profile
   */
  async getOrCreateTenantProfile(customerId: string, tenantId: string): Promise<CustomerTenantProfile> {
    let profile = await this.profileRepository.findOne({
      where: { customer_id: customerId, tenant_id: tenantId }
    });

    if (!profile) {
      profile = this.profileRepository.create({
        customer_id: customerId,
        tenant_id: tenantId,
        preferences: {},
        loyalty_points: 0,
        total_orders: 0,
        total_spent: 0
      });
      await this.profileRepository.save(profile);
    }

    return profile;
  }

  /**
   * Update customer tenant profile after order
   */
  async updateProfileAfterOrder(customerId: string, tenantId: string, orderAmount: number): Promise<void> {
    const profile = await this.getOrCreateTenantProfile(customerId, tenantId);

    profile.total_orders += 1;
    profile.total_spent += orderAmount;
    profile.last_order_at = new Date();

    if (!profile.first_order_at) {
      profile.first_order_at = new Date();
    }

    await this.profileRepository.save(profile);
  }

  /**
   * Get customer analytics across all tenants
   */
  async getCustomerAnalytics(customerId: string): Promise<CustomerAnalytics> {
    const profiles = await this.profileRepository.find({
      where: { customer_id: customerId },
      relations: ['tenant']
    });

    const totalSpent = profiles.reduce((sum, profile) => sum + Number(profile.total_spent), 0);
    const totalOrders = profiles.reduce((sum, profile) => sum + profile.total_orders, 0);
    const totalLoyaltyPoints = profiles.reduce((sum, profile) => sum + profile.loyalty_points, 0);

    return {
      totalSpent,
      totalOrders,
      totalLoyaltyPoints,
      activeTenants: profiles.length,
      favoriteVendor: this.findFavoriteVendor(profiles),
      tenantBreakdown: profiles.map(profile => ({
        tenantId: profile.tenant_id,
        tenantName: profile.tenant?.name,
        totalSpent: Number(profile.total_spent),
        totalOrders: profile.total_orders,
        loyaltyPoints: profile.loyalty_points
      }))
    };
  }

  private findFavoriteVendor(profiles: CustomerTenantProfile[]): { tenantId: string; tenantName: string } | null {
    if (profiles.length === 0) return null;

    const favorite = profiles.reduce((max, profile) =>
      Number(profile.total_spent) > Number(max.total_spent) ? profile : max
    );

    return {
      tenantId: favorite.tenant_id,
      tenantName: favorite.tenant?.name || 'Unknown'
    };
  }
}

interface CustomerAnalytics {
  totalSpent: number;
  totalOrders: number;
  totalLoyaltyPoints: number;
  activeTenants: number;
  favoriteVendor: { tenantId: string; tenantName: string } | null;
  tenantBreakdown: Array<{
    tenantId: string;
    tenantName: string;
    totalSpent: number;
    totalOrders: number;
    loyaltyPoints: number;
  }>;
}
```