# Multi-Tenant E-commerce API: Technical Architecture

## System Architecture Overview

The Multi-Tenant E-commerce API is built on a modern, scalable architecture using NestJS as the primary framework. This document outlines the technical architecture, design patterns, and implementation details.

```
┌─────────────────────────────────────────────────────────────┐
│                        Client Applications                   │
└───────────────────────────────┬─────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                           API Gateway                        │
└───────────────────────────────┬─────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                        NestJS Application                    │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │    Auth     │  │    Users    │  │       Tenants       │  │
│  │   Module    │  │   Module    │  │       Module        │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │  Products   │  │  Categories │  │       Orders        │  │
│  │   Module    │  │   Module    │  │       Module        │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└───────────────────────────────┬─────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      Database Layer                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │  Main Database  │  │ Tenant Database │  │    Tenant   │  │
│  │  (PostgreSQL)   │  │  1 (PostgreSQL) │  │ Database N  │  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## Core Technologies

- **Framework**: NestJS (Node.js)
- **Language**: TypeScript
- **ORM**: TypeORM
- **Database**: PostgreSQL
- **Authentication**: JWT (JSON Web Tokens)
- **API Documentation**: Swagger/OpenAPI
- **Validation**: class-validator
- **Security**: Helmet, CSRF protection, rate limiting

## Multi-Tenancy Implementation

### Database-per-Tenant Approach

The system uses a database-per-tenant approach, where:

1. A main database stores:
   - User accounts
   - Vendor/tenant information
   - Shared catalog data (categories)
   - list products from all vendors

2. Each tenant gets a dedicated database for:
   - Tenant-specific products
   - Tenant-specific orders
   - Other tenant-specific data

### Tenant Identification and Routing

1. **Subdomain-based Identification**:
   - Each tenant is identified by a unique subdomain (e.g., `vendor1.example.com`)
   - The `TenantMiddleware` extracts the subdomain from the request host header

2. **Dynamic Connection Management**:
   - The `TenantsService` maintains a connection pool for tenant databases
   - Connections are created on-demand and cached for performance
   - The service provides methods to get the appropriate database connection for the current tenant

## Module Structure

### Auth Module

Handles authentication and authorization:
- User registration for different roles
- Login with JWT token generation
- Token refresh mechanism
- Role-based access control

### Users Module

Manages user accounts:
- Super admin management
- Vendor management
- Customer management
- User profile operations

### Tenants Module

Manages tenant operations:
- Tenant creation and configuration
- Tenant database provisioning
- Tenant lookup and information

### Products Module (Planned)

Will handle product management:
- Product CRUD operations
- Product categorization
- Product search and filtering

### Categories Module (Planned)

Will manage product categories:
- Category hierarchy (categories and subcategories)
- Category CRUD operations

### Orders Module (Planned)

Will handle order processing:
- Order creation
- Order status management
- Order history

## Database Schema

### Main Database

```
┌─────────────┐       ┌─────────────┐       ┌─────────────┐
│    users    │       │   vendors   │       │  customers  │
├─────────────┤       ├─────────────┤       ├─────────────┤
│ id          │       │ id          │       │ id          │
│ email       │◄──┐   │ storeName   │       │ firstName   │
│ password    │   │   │ description │       │ lastName    │
│ role        │   │   │ logo        │       │ phone       │
│ isActive    │   │   │ contactPhone│       │ address     │
│ loginAttempts│  │   │ contactEmail│       │ city        │
│ refreshToken│   │   │ address     │       │ state       │
│ lastLoginIp │   │   │ userId      │◄──┘   │ country     │
└─────────────┘   │   │ subdomain   │       │ zipCode     │
                  │   │ databaseName│       │ userId      │◄──┘
                  │   │ isActive    │       └─────────────┘
                  │   └─────────────┘
```

### Tenant-Specific Databases

Each tenant database will contain:

```
┌─────────────┐       ┌─────────────┐       ┌─────────────┐
│   products  │       │    orders   │       │ order_items │
├─────────────┤       ├─────────────┤       ├─────────────┤
│ id          │       │ id          │       │ id          │
│ name        │       │ customerId  │       │ orderId     │
│ description │       │ status      │       │ productId   │
│ price       │       │ totalAmount │       │ quantity    │
│ categoryId  │       │ createdAt   │       │ price       │
│ stock       │       │ updatedAt   │       └─────────────┘
│ images      │◄──┐   └─────────────┘
└─────────────┘   │         │
                  │         │
                  │         ▼
                  │   ┌─────────────┐
                  └───┤ order_items │
                      └─────────────┘
```

## Product Data Architecture

### Main Platform (`example.com`)
- The main database maintains references to all products across all vendors
- Product queries are optimized for marketplace-wide search and filtering
- Each product record includes:
  - Core product data
  - Vendor reference
  - Visibility flags
  - Global search indexing

### Vendor Subdomains (`vendorname.example.com`)
- Each vendor's database contains only their specific products
- Dedicated database per vendor ensures:
  - Data isolation
  - Independent scaling
  - Vendor-specific customizations
  - Optimized queries for single-vendor operations

### Data Flow
```
┌─────────────────────┐
│   Main Platform     │
│   (example.com)     │
│                     │
│ ┌─────┐ ┌─────┐    │
│ │Prod1│ │Prod2│    │
│ └──┬──┘ └──┬──┘    │
│    │       │       │
│    ▼       ▼       │
│  All Products      │
└─────────────────────┘
        ▲
        │ Sync
        ▼
┌─────────────────────┐
│  Vendor Subdomains  │
│                     │
│ ┌─────────────────┐ │
│ │ vendor1.example │ │
│ │   (Prod1 only)  │ │
│ └─────────────────┘ │
│ ┌─────────────────┐ │
│ │ vendor2.example │ │
│ │   (Prod2 only)  │ │
│ └─────────────────┘ │
└─────────────────────┘
```

## Security Implementation

### Authentication

1. **JWT-based Authentication**:
   - Access tokens with short lifespan (configurable)
   - Refresh tokens for extended sessions
   - Token invalidation on logout

2. **Password Security**:
   - Passwords hashed using bcrypt
   - Password strength validation

### Request Protection

1. **Rate Limiting**:
   - Implemented using @nestjs/throttler
   - Configurable limits per endpoint
   - Protection against brute force attacks

2. **Input Validation**:
   - All DTOs validated using class-validator
   - Strict type checking with TypeScript

3. **Security Headers**:
   - Helmet middleware for HTTP security headers
   - CSRF protection (temporarily disabled for debugging)

## Error Handling

The application implements a comprehensive error handling strategy:

1. **HTTP Exceptions**:
   - Proper HTTP status codes for different error scenarios
   - Detailed error messages for debugging

2. **Validation Errors**:
   - Automatic validation using ValidationPipe
   - Detailed validation error messages

3. **Database Errors**:
   - Try-catch blocks for database operations
   - Proper error mapping to HTTP exceptions

## Scalability Considerations

1. **Connection Pooling**:
   - TypeORM connection pools for database connections
   - Efficient reuse of database connections

2. **Caching**:
   - Tenant connection caching
   - Potential for implementing Redis caching for frequently accessed data

3. **Horizontal Scaling**:
   - Stateless API design allows for horizontal scaling
   - JWT authentication supports distributed deployment
