import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { User } from '../../../modules/users/entities/user.entity';
import { UserRole } from '../../../common/interfaces/user-roles.enum';
import { BaseSeeder } from './base.seeder';

@Injectable()
export class UsersSeeder extends BaseSeeder {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>
  ) {
    super();
  }

  async seed(): Promise<void> {
    try {
      // Create super admin user
      await this.createUser();
      console.log('Super admin user seeding completed');
    } catch (error) {
      console.error('Error seeding super admin user:', error);
    }
  }

  private async createUser(): Promise<void> {
    const adminData: Partial<User> = {
      email: '<EMAIL>',
      password: await bcrypt.hash('Admin@123', 10),
      role: UserRole.SUPER_ADMIN,
      isActive: true
    };

    const existingAdmin = await this.userRepository.findOne({
      where: { email: adminData.email }
    });

    if (!existingAdmin) {
      const admin = this.userRepository.create(adminData);
      await this.userRepository.save(admin);
    }
  }
}
