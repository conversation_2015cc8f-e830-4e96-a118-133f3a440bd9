import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  Req,
  Get,
  UseGuards,
  InternalServerErrorException,
} from '@nestjs/common';
import { Request } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
} from '@nestjs/swagger';
import { User } from '../users/entities/user.entity';
import { Vendor } from '../users/entities/vendor.entity';
import { Customer } from '../users/entities/customer.entity';
import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { VendorResponseDto } from '../users/dto/vendor-response.dto';
import { CreateUserDto } from '../users/dto/create-user.dto';
import { CreateVendorDto } from '../users/dto/create-vendor.dto';
import { CreateCustomerDto } from '../users/dto/create-customer.dto';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { LogoutResponseDto } from './dto/logout-response.dto';
import { UserRole } from '../../common/interfaces/user-roles.enum';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  // Super Admin Routes
  @Post('superadmin/login')
  @ApiOperation({ summary: 'Super Admin Login' })
  @ApiResponse({ status: 200, description: 'Super admin successfully logged in' })
  @ApiResponse({ status: 401, description: 'Invalid credentials' })
  @ApiResponse({ status: 403, description: 'Account temporarily locked' })
  @ApiBody({ type: LoginDto })
  @HttpCode(HttpStatus.OK)
  async loginSuperAdmin(@Body() loginDto: LoginDto, @Req() req: Request) {
    return await this.authService.loginWithRole(
      loginDto,
      req,
      UserRole.SUPER_ADMIN,
    );
  }

  @Post('superadmin/register')
  @ApiOperation({ summary: 'Register Super Admin (Initial Setup Only)' })
  @ApiResponse({ status: 201, description: 'Super admin successfully registered', type: User })
  @ApiResponse({ status: 400, description: 'Invalid input or passwords do not match' })
  @ApiResponse({ status: 409, description: 'Email already exists' })
  @ApiBody({ type: CreateUserDto })
  async registerSuperAdmin(@Body() createUserDto: CreateUserDto): Promise<User> {
    return await this.authService.registerSuperAdmin(createUserDto);
  }

  // Vendor Routes
  @Post('vendor/login')
  @ApiOperation({ summary: 'Vendor Login' })
  @ApiResponse({
    status: 200,
    description: 'Vendor successfully logged in',
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid credentials or wrong user role',
  })
  @ApiResponse({
    status: 403,
    description: 'Account temporarily locked',
  })
  @ApiBody({ type: LoginDto })
  @HttpCode(HttpStatus.OK)
  async loginVendor(@Body() loginDto: LoginDto, @Req() req: Request) {
    return await this.authService.loginWithRole(loginDto, req, UserRole.VENDOR);
  }

  @Post('vendor/register')
  @ApiOperation({ summary: 'Register New Vendor' })
  @ApiResponse({
    status: 201,
    description: 'Vendor successfully registered',
    type: VendorResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input or passwords do not match',
  })
  @ApiResponse({
    status: 409,
    description: 'Email or store name already exists',
  })
  @ApiBody({ type: CreateVendorDto })
  async registerVendor(@Body() createVendorDto: CreateVendorDto): Promise<VendorResponseDto> {
    return await this.authService.registerVendor(createVendorDto);
  }

  // Customer Routes
  @Post('customer/login')
  @ApiOperation({
    summary: 'Customer Login',
    description: 'Login endpoint for customers. Accessible through both main portal and vendor subdomains.',
  })
  @ApiResponse({
    status: 200,
    description: 'Customer successfully logged in',
    schema: {
      type: 'object',
      properties: {
        access_token: { type: 'string' },
        refresh_token: { type: 'string' },
        user: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            role: { type: 'string', enum: ['customer'] },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid credentials or wrong user role',
  })
  @ApiResponse({
    status: 403,
    description: 'Account temporarily locked',
  })
  @ApiBody({ type: LoginDto })
  @HttpCode(HttpStatus.OK)
  async loginCustomer(@Body() loginDto: LoginDto, @Req() req: Request) {
    return await this.authService.loginWithRole(
      loginDto,
      req,
      UserRole.CUSTOMER,
    );
  }

  @Post('customer/register')
  @ApiOperation({
    summary: 'Register New Customer',
    description: 
      'Register a new customer account. When registering through a vendor subdomain, ' +
      'the subdomain is recorded as the registration source.',
  })
  @ApiResponse({
    status: 201,
    description: 'Customer successfully registered',
    type: Customer,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input or passwords do not match',
  })
  @ApiResponse({
    status: 409,
    description: 'Email already exists',
  })
  @ApiResponse({
    status: 403,
    description: 'Invalid or inactive vendor subdomain',
  })
  @ApiBody({ type: CreateCustomerDto })
  async registerCustomer(
    @Body() createCustomerDto: CreateCustomerDto,
    @Req() req: Request,
  ): Promise<Customer> {
    const host = req.headers.host;
    const registrationSource = host;
    return await this.authService.registerCustomer(
      createCustomerDto,
      registrationSource,
    );
  }

  // Common Routes
  @Post('refresh')
  @ApiOperation({ summary: 'Refresh Access Token' })
  @ApiResponse({
    status: 200,
    description: 'New access token generated successfully',
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid or expired refresh token',
  })
  @ApiBody({ type: RefreshTokenDto })
  async refreshToken(
    @Body() refreshTokenDto: RefreshTokenDto,
    @Req() req: Request,
  ) {
    return await this.authService.refreshToken(
      refreshTokenDto.refreshToken,
      req,
    );
  }

  @Post('logout')
  @ApiOperation({ summary: 'Logout User' })
  @ApiResponse({
    status: 200,
    description: 'Successfully logged out',
    type: LogoutResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @UseGuards(JwtAuthGuard)
  async logout(@CurrentUser() user: { id: string }): Promise<LogoutResponseDto> {
    try {
      await this.authService.logout(user.id);
      return { message: 'Successfully logged out' };
    } catch (error: unknown) {
      if (error instanceof Error) {
        throw new InternalServerErrorException(
          `Logout failed: ${error.message}`,
        );
      }
      throw new InternalServerErrorException(
        'Logout failed due to an unknown error',
      );
    }
  }

  @Get('profile')
  @ApiOperation({ summary: 'Get Current User Profile' })
  @ApiResponse({
    status: 200,
    description: 'Returns the authenticated user profile',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @UseGuards(JwtAuthGuard)
  getProfile(
    @CurrentUser() user: { id: string; email: string; role: string },
  ): { id: string; email: string; role: string } {
    return user;
  }
}
