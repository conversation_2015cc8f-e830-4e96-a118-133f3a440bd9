import {
  <PERSON>ti<PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  ManyToOne,
  JoinColumn
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Category } from '../../categories/entities/category.entity';
import { Vendor } from '../../users/entities/vendor.entity';

@Entity('product_catalog')
@Index(['tenantSubdomain', 'tenantProductId'], { unique: true })
@Index(['isActive', 'createdAt'])
@Index(['categoryId', 'price'])
@Index(['vendorId', 'isActive'])
export class ProductCatalog {
  @ApiProperty({
    description: 'Unique identifier for the catalog entry',
    example: '3cbd87ba-7a13-4abf-aafa-c72bb2b7eeb6',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'Product ID from tenant database',
    example: '3cbd87ba-7a13-4abf-aafa-c72bb2b7eeb6',
  })
  @Column('uuid')
  tenantProductId: string;

  @ApiProperty({
    description: 'Vendor ID',
    example: '3cbd87ba-7a13-4abf-aafa-c72bb2b7eeb6',
  })
  @Column('uuid')
  vendorId: string;

  @ApiProperty({
    description: 'Tenant subdomain',
    example: 'demo',
  })
  @Column()
  tenantSubdomain: string;

  @ApiProperty({
    description: 'Product name',
    example: 'Gaming Laptop',
  })
  @Column()
  name: string;

  @ApiProperty({
    description: 'Product description',
    example: 'High-performance gaming laptop with RTX graphics',
    required: false,
  })
  @Column('text', { nullable: true })
  description: string;

  @ApiProperty({
    description: 'Product price',
    example: 1299.99,
  })
  @Column('decimal', { precision: 12, scale: 2 })
  price: number;

  @ApiProperty({
    description: 'Stock quantity',
    example: 10,
  })
  @Column('int', { default: 0 })
  stock: number;

  @ApiProperty({
    description: 'Product images',
    example: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
    isArray: true,
  })
  @Column('text', { array: true, default: '{}' })
  images: string[];

  @ApiProperty({
    description: 'Category ID',
    example: '3cbd87ba-7a13-4abf-aafa-c72bb2b7eeb6',
    required: false,
  })
  @Column('uuid', { nullable: true })
  categoryId: string;

  @ApiProperty({
    description: 'Subcategory ID',
    example: '3cbd87ba-7a13-4abf-aafa-c72bb2b7eeb6',
    required: false,
  })
  @Column('uuid', { nullable: true })
  subcategoryId: string;

  @ApiProperty({
    description: 'Whether the product is active',
    example: true,
  })
  @Column({ default: true })
  isActive: boolean;

  @ApiProperty({
    description: 'Full-text search vector',
    required: false,
  })
  @Column('tsvector', { nullable: true })
  searchVector: string;

  @ApiProperty({
    description: 'Cached category path for performance',
    example: 'Electronics > Computers > Laptops',
    required: false,
  })
  @Column('text', { nullable: true })
  categoryPath: string;

  @ApiProperty({
    description: 'Stock status for quick filtering',
    example: 'in_stock',
    required: false,
  })
  @Column({ nullable: true })
  stockStatus: string;

  @ApiProperty({
    description: 'Product attributes as JSON',
    example: { brand: 'Dell', color: 'Black', weight: '2.5kg' },
    required: false,
  })
  @Column('jsonb', { nullable: true })
  attributes: Record<string, any>;

  @ApiProperty({
    description: 'When this entry was created',
    example: '2025-05-26T09:30:00.000Z',
  })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({
    description: 'When this entry was last updated',
    example: '2025-05-26T09:30:00.000Z',
  })
  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty({
    description: 'When this entry was last synced from tenant database',
    example: '2025-05-26T09:30:00.000Z',
  })
  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  syncedAt: Date;

  // Relations
  @ManyToOne(() => Category, { nullable: true })
  @JoinColumn({ name: 'categoryId' })
  category?: Category;

  @ManyToOne(() => Vendor)
  @JoinColumn({ name: 'vendorId' })
  vendor?: Vendor;
}
