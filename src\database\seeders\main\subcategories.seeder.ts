import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Category } from '../../../modules/categories/entities/category.entity';
import { Subcategory } from '../../../modules/categories/entities/subcategory.entity';
import { BaseSeeder } from './base.seeder';

@Injectable()
export class SubcategoriesSeeder extends BaseSeeder {
  constructor(
    @InjectRepository(Category)
    private readonly categoryRepository: Repository<Category>,
    @InjectRepository(Subcategory)
    private readonly subcategoryRepository: Repository<Subcategory>
  ) {
    super();
  }

  async seed(): Promise<void> {
    try {
      const categories = await this.categoryRepository.find();
      
      const subcategoriesData = {
        'Electronics': [
          'Smartphones',
          'Laptops',
          'Cameras',
          'Audio Devices',
          'Gaming Consoles'
        ],
        'Fashion': [
          'Men\'s Clothing',
          'Women\'s Clothing',
          'Kids\' Clothing',
          'Shoes',
          'Accessories'
        ],
        'Home & Garden': [
          'Furniture',
          'Kitchen & Dining',
          'Bedding',
          'Garden Tools',
          'Home Decor'
        ],
        'Books': [
          'Fiction',
          'Non-Fiction',
          'Educational',
          'Children\'s Books',
          'E-Books'
        ],
        'Sports & Outdoors': [
          'Fitness Equipment',
          'Camping Gear',
          'Team Sports',
          'Water Sports',
          'Cycling'
        ]
      };

      for (const category of categories) {
        const subcategories = subcategoriesData[category.name] || [];
        
        for (const subcategoryName of subcategories) {
          const existingSubcategory = await this.subcategoryRepository.findOne({
            where: {
              name: subcategoryName,
              categoryId: category.id
            }
          });

          if (!existingSubcategory) {
            const subcategory = this.subcategoryRepository.create({
              name: subcategoryName,
              description: `${subcategoryName} in ${category.name} category`,
              image: `https://example.com/${subcategoryName.toLowerCase().replace(/\s+/g, '-')}.jpg`,
              isActive: true,
              categoryId: category.id
            });

            await this.subcategoryRepository.save(subcategory);
          }
        }
      }

      console.log('Subcategories seeding completed');
    } catch (error) {
      console.error('Error seeding subcategories:', error);
    }
  }
}
