import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UsersService } from './users.service';
import { User } from './entities/user.entity';
import { Vendor } from './entities/vendor.entity';
import { Customer } from './entities/customer.entity';
import { SuperAdminSeeder } from './seeders/super-admin.seeder';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Vendor, Customer]),
  ],
  providers: [UsersService, SuperAdminSeeder],
  exports: [UsersService, SuperAdminSeeder],
})
export class UsersModule {
  constructor(private readonly superAdminSeeder: SuperAdminSeeder) {}

  async onModuleInit() {
    try {
      // Seed super admin user
      await this.superAdminSeeder.seed();
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error(`Error during module initialization: ${error.message}`);
      } else {
        console.error('Unknown error during module initialization');
      }
    }
  }
}
