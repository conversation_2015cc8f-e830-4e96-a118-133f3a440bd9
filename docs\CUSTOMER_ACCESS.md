# Customer Access in Multi-Tenant E-commerce Platform

## Overview
Customers have flexible access options in our multi-tenant e-commerce platform. They can:
1. Access through the main platform (`example.com`)
2. Access through vendor-specific stores (`vendorname.example.com`)

## Product Visibility

### Main Portal Access (`example.com`)
- The main platform acts as a marketplace, displaying products from all vendors
- Customers can browse and search through the complete product catalog
- Products are clearly marked with their respective vendor information
- Unified shopping experience with access to all available products

### Vendor Store Access (`vendorname.example.com`)
- Each vendor subdomain shows only that specific vendor's products
- Focused shopping experience within a single vendor's catalog
- Custom branding and store-specific features
- Direct access to vendor-specific promotions and offers

## Customer Authentication

### Main Portal Access
- Customers can register and login through the main platform
- URL: `https://example.com/auth/customer/*`
- All registered customers can access any vendor store
- Single account works across all vendor stores

### Vendor Store Access
- Customers can register and login directly through vendor subdomains
- URL: `https://vendorname.example.com/auth/customer/*`
- Registration through a vendor subdomain will:
  - Create a customer account in the main platform
  - Record the vendor store as the registration source
  - Allow immediate access to the vendor's store
- Customers can use these credentials to access:
  - The original vendor store where they registered
  - The main platform
  - Other vendor stores

## Implementation Details

### Registration
- All customer accounts are stored in the main database
- Registration source (vendor subdomain) is recorded if applicable
- Passwords are securely hashed and stored
- Email addresses must be unique across the entire platform

### Login
- Customers can log in from any portal (main or vendor subdomains)
- The system validates:
  1. If logging in through a vendor subdomain:
     - Verifies the vendor exists and is active
  2. Authenticates the customer credentials
  3. Issues JWT tokens for access

### Session Management
- JWT tokens are valid across all portals
- Refresh tokens enable extended sessions
- Logout invalidates tokens platform-wide

## Security Considerations
- All customer data is centralized in the main database
- Vendor-specific data is isolated in separate databases
- Authentication is handled by the main platform
- CORS is properly configured for subdomain access
