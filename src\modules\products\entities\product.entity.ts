import { <PERSON>tity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('products')
export class Product {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column('text', { nullable: true })
  description: string;

  @Column('decimal', { precision: 10, scale: 2 })
  price: number;

  @Column({ type: 'int', default: 0 })
  stock: number;

  @Column('text', { array: true, nullable: true, default: '{}' })
  images: string[];

  @Column({ default: true })
  isActive: boolean;

  @Column('uuid', { nullable: true })
  categoryId: string; // Reference to category in main database (no FK constraint)

  @Column({ type: 'uuid' })
  vendorId: string; // Reference to vendor in main database (no FK constraint)

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
