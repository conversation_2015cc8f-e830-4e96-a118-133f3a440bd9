# Changelog

All notable changes to the Multi-Tenant E-commerce API project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Initial project setup with NestJS, TypeScript, TypeORM, and PostgreSQL
- Configuration for environment variables and database connection
- User management module with entities for different user types (super-admin, vendor, customer)
- Authentication module with JWT strategy
- Multi-tenancy support with database-per-tenant approach
- Tenant management with subdomain handling
- Role-based access control
- Basic e-commerce entities (categories, products, orders)
- Swagger/OpenAPI documentation with detailed API descriptions
- API endpoint documentation with request/response examples
- Bearer token authentication in Swagger UI
- Enhanced JWT authentication with refresh tokens
- Login attempt limiting and account locking
- IP and user agent tracking for security
- User agent interceptor for request tracking
- Rate limiting with @nestjs/throttler to prevent brute force attacks
- Secure HTTP headers with Helmet
- CSRF protection with csurf
- Comprehensive security configuration in environment variables

### Fixed
- Fixed TypeScript errors in JWT strategy by properly handling undefined secretOrKey
- Fixed TenantConnection issues in the tenants service by properly handling undefined values
- Fixed dependency injection issue with SuperAdminSeeder by moving it to the UsersModule
- Updated database connection configuration with proper default values
- Fixed vendor registration by automatically generating subdomain and database name
- Fixed tenant database creation during vendor registration
- Fixed database name generation to be PostgreSQL-compatible (replacing hyphens with underscores)
- Fixed super admin seeder to use consistent default password
- Added proper error handling for database operations
- Fixed JWT configuration issues with signOptions
- Fixed refresh token implementation
- Fixed IP address handling in authentication
- Fixed date comparison issues in login attempts
- Fixed TypeScript issues with Helmet and CSRF implementations
- Fixed CSRF configuration to work with Swagger UI
- Added custom type declarations for third-party modules
- Updated tsconfig.json to enable esModuleInterop for better module compatibility
- Temporarily disabled Helmet and CSRF for debugging purposes
- Fixed TypeScript 'any' types in interceptors and strategies
- Added proper type declarations for request objects
- Improved error handling with proper TypeScript unknown type
- Created AuthRequest interface for better type safety
- Improved logout functionality with proper error handling
- Enhanced Swagger documentation for the logout endpoint
- Added LogoutResponseDto for better API documentation
- Used proper NestJS exception handling in the auth controller

### Improved
- Implemented proper database migrations instead of relying on schema synchronization
- Maintained non-nullable foreign keys to ensure data integrity
- Created tables in the correct order with inline foreign key constraints
- Added conditional dropSchema option for development environment only
- Added proper error handling for database operations
- Consolidated vendor and tenant entities since they represent the same concept
- Simplified database schema by removing redundant tenant table
- Enhanced code organization and reduced duplication
- Removed redundant POST /tenants endpoint since tenants are created automatically during vendor registration
- Improved TypeScript type safety by adding proper return types and interfaces
- Added custom interfaces for better type checking (JwtPayload, TenantRequest)
- Eliminated 'any' types throughout the codebase for better type safety
- Added proper method return types for better code documentation and IDE support
- Enhanced security with proper token invalidation on logout
- Improved error handling in authentication flow
- Added consistent IP address handling across the application
- Enhanced type safety in authentication service
- Implemented comprehensive security measures following OWASP best practices
- Added proper TypeScript error handling for unknown error types
