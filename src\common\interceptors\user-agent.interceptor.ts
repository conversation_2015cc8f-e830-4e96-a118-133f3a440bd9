import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { UsersService } from '../../modules/users/users.service';

@Injectable()
export class UserAgentInterceptor implements NestInterceptor {
  constructor(private readonly usersService: UsersService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<unknown> {
    const request = context.switchToHttp().getRequest();
    const user = request.user as { id: string } | undefined;

    if (user) {
      const userAgent = request.headers['user-agent'];
      const ip = request.ip ? String(request.ip) : 'unknown';
      this.usersService.updateLastLoginInfo(user.id, ip, userAgent);
    }

    return next.handle();
  }
}