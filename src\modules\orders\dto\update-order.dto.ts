import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsString, IsObject, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { OrderStatus, PaymentStatus } from '../entities/order.entity';
import { AddressDto } from './create-order.dto';

export class UpdateOrderDto {
  @ApiProperty({
    description: 'Order status',
    enum: OrderStatus,
    example: OrderStatus.CONFIRMED,
    required: false,
  })
  @IsOptional()
  @IsEnum(OrderStatus)
  status?: OrderStatus;

  @ApiProperty({
    description: 'Payment status',
    enum: PaymentStatus,
    example: PaymentStatus.PAID,
    required: false,
  })
  @IsOptional()
  @IsEnum(PaymentStatus)
  paymentStatus?: PaymentStatus;

  @ApiProperty({
    description: 'Shipping address',
    type: AddressDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => AddressDto)
  shippingAddress?: AddressDto;

  @ApiProperty({
    description: 'Billing address',
    type: AddressDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => AddressDto)
  billingAddress?: AddressDto;

  @ApiProperty({
    description: 'Order notes',
    example: 'Updated delivery instructions',
    required: false,
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({
    description: 'Tracking number',
    example: 'TRK123456789',
    required: false,
  })
  @IsOptional()
  @IsString()
  trackingNumber?: string;

  @ApiProperty({
    description: 'Payment method information',
    example: {
      type: 'credit_card',
      last4: '1234',
      brand: 'visa',
      transactionId: 'txn_123456',
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  paymentMethod?: {
    type: string;
    last4?: string;
    brand?: string;
    transactionId?: string;
  };
}
