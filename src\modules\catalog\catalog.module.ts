import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CacheModule } from '@nestjs/cache-manager';
import { ProductCatalog } from './entities/product-catalog.entity';
import { CatalogService } from './services/catalog.service';
import { CatalogSyncService } from './services/catalog-sync.service';
import { CatalogController } from './catalog.controller';
import { TenantsModule } from '../tenants/tenants.module';
import { CqrsModule } from '@nestjs/cqrs';
import {
  ProductCreatedHandler,
  ProductUpdatedHandler,
  ProductDeletedHandler,
  ProductStatusChangedHandler,
} from './handlers/product-sync.handler';

@Module({
  imports: [
    TypeOrmModule.forFeature([ProductCatalog]),
    CacheModule.register(),
    TenantsModule,
    CqrsModule,
  ],
  controllers: [CatalogController],
  providers: [
    CatalogService,
    CatalogSyncService,
    ProductCreatedHandler,
    ProductUpdatedHandler,
    ProductDeletedHandler,
    ProductStatusChangedHandler,
  ],
  exports: [CatalogService, CatalogSyncService],
})
export class CatalogModule {}
