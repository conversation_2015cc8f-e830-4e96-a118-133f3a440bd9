{"info": {"name": "Multi-Tenant-Ecommerce-API", "description": "API collection for the Multi-Tenant E-commerce platform with Performance-Optimized Architecture (90-98% faster queries)", "version": "2.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "description": "Authentication endpoints for different user roles", "item": [{"name": "Super Admin", "item": [{"name": "Super Admin Login", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/auth/superadmin/login", "path": ["auth", "superadmin", "login"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}}}, {"name": "Register Super Admin", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/auth/superadmin/register", "path": ["auth", "superadmin", "register"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"passwordConfirmation\": \"password123\"\n}"}}}]}, {"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/auth/vendor/login", "path": ["auth", "vendor", "login"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}}}, {"name": "Register Vendor", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/auth/vendor/register", "path": ["auth", "vendor", "register"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"passwordConfirmation\": \"password123\",\n  \"storeName\": \"My Store\",\n  \"description\": \"My store description\",\n  \"subdomain\": \"mystore\"\n}"}}}]}, {"name": "Customer", "item": [{"name": "Customer <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/auth/customer/login", "path": ["auth", "customer", "login"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}}}, {"name": "Register Customer", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/auth/customer/register", "path": ["auth", "customer", "register"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"passwordConfirmation\": \"password123\"\n}"}}}]}, {"name": "Common", "item": [{"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/auth/refresh", "path": ["auth", "refresh"]}, "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"your-refresh-token\"\n}"}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/auth/logout", "path": ["auth", "logout"]}}}, {"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/auth/profile", "path": ["auth", "profile"]}}}]}]}, {"name": "Products", "description": "Product management endpoints", "item": [{"name": "Get All Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products", "path": ["products"]}}}, {"name": "Get Product by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/:id", "path": ["products", ":id"], "variable": [{"key": "id", "value": ""}]}}}, {"name": "Create Product", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/products", "path": ["products"]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Product Name\",\n  \"description\": \"Product Description\",\n  \"price\": 99.99,\n  \"stock\": 100,\n  \"categoryId\": \"category-uuid\",\n  \"images\": [\"image1.jpg\", \"image2.jpg\"]\n}"}}}, {"name": "Update Product", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/products/:id", "path": ["products", ":id"], "variable": [{"key": "id", "value": ""}]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Product Name\",\n  \"price\": 149.99,\n  \"stock\": 200\n}"}}}, {"name": "Delete Product", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/products/:id", "path": ["products", ":id"], "variable": [{"key": "id", "value": ""}]}}}]}, {"name": "🚀 Enhanced Products (90-98% Faster)", "description": "Performance-optimized product endpoints using catalog architecture", "item": [{"name": "Get All Products (Optimized)", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/enhanced?page=1&limit=20&sortBy=createdAt&sortOrder=DESC", "path": ["products", "enhanced"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "20", "description": "Items per page (max: 100)"}, {"key": "categoryId", "value": "", "description": "Filter by category ID", "disabled": true}, {"key": "vendorId", "value": "", "description": "Filter by vendor ID", "disabled": true}, {"key": "minPrice", "value": "", "description": "Minimum price filter", "disabled": true}, {"key": "maxPrice", "value": "", "description": "Maximum price filter", "disabled": true}, {"key": "sortBy", "value": "createdAt", "description": "Sort field: name, price, createdAt, updatedAt"}, {"key": "sortOrder", "value": "DESC", "description": "Sort order: ASC, DESC"}]}}}, {"name": "Search Products (Full-text)", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/enhanced/search?q=laptop&page=1&limit=20", "path": ["products", "enhanced", "search"], "query": [{"key": "q", "value": "laptop", "description": "Search query (required)"}, {"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "20", "description": "Items per page"}, {"key": "categoryId", "value": "", "description": "Filter by category", "disabled": true}, {"key": "minPrice", "value": "", "description": "Minimum price", "disabled": true}, {"key": "maxPrice", "value": "", "description": "Maximum price", "disabled": true}]}}}, {"name": "Get Products by Vendor", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/enhanced/vendor/:vendorId?page=1&limit=20", "path": ["products", "enhanced", "vendor", ":vendorId"], "variable": [{"key": "vendorId", "value": "", "description": "Vendor UUID"}], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "Get Products by Category", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/enhanced/category/:categoryId?page=1&limit=20", "path": ["products", "enhanced", "category", ":categoryId"], "variable": [{"key": "categoryId", "value": "", "description": "Category UUID"}], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "Get Product Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/enhanced/:id", "path": ["products", "enhanced", ":id"], "variable": [{"key": "id", "value": "", "description": "Product UUID"}]}}}, {"name": "Create Product (Enhanced)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/products/enhanced", "path": ["products", "enhanced"]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Gaming Laptop\",\n  \"description\": \"High-performance gaming laptop with RTX graphics\",\n  \"price\": 1299.99,\n  \"stock\": 10,\n  \"images\": [\"https://example.com/laptop1.jpg\", \"https://example.com/laptop2.jpg\"],\n  \"categoryId\": \"category-uuid-here\",\n  \"isActive\": true\n}"}}}, {"name": "Update Product (Enhanced)", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/products/enhanced/:id", "path": ["products", "enhanced", ":id"], "variable": [{"key": "id", "value": "", "description": "Product UUID"}]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Gaming Laptop\",\n  \"price\": 1199.99,\n  \"stock\": 15\n}"}}}, {"name": "Update Product Status", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/products/enhanced/:id/status", "path": ["products", "enhanced", ":id", "status"], "variable": [{"key": "id", "value": "", "description": "Product UUID"}]}, "body": {"mode": "raw", "raw": "{\n  \"isActive\": false\n}"}}}, {"name": "Delete Product (Enhanced)", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/products/enhanced/:id", "path": ["products", "enhanced", ":id"], "variable": [{"key": "id", "value": "", "description": "Product UUID"}]}}}, {"name": "Get Vendor Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/products/enhanced/vendor/:vendorId/stats", "path": ["products", "enhanced", "vendor", ":vendorId", "stats"], "variable": [{"key": "vendorId", "value": "", "description": "Vendor UUID"}]}}}]}, {"name": "📦 Orders", "description": "Complete order management system", "item": [{"name": "Get All Orders", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/orders?customerId=", "path": ["orders"], "query": [{"key": "customerId", "value": "", "description": "Filter by customer ID (vendor only)", "disabled": true}]}}}, {"name": "Get Order by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/orders/:id", "path": ["orders", ":id"], "variable": [{"key": "id", "value": "", "description": "Order UUID"}]}}}, {"name": "Create Order", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/orders", "path": ["orders"]}, "body": {"mode": "raw", "raw": "{\n  \"customerId\": \"customer-uuid-here\",\n  \"items\": [\n    {\n      \"productId\": \"product-uuid-here\",\n      \"quantity\": 2\n    },\n    {\n      \"productId\": \"another-product-uuid\",\n      \"quantity\": 1\n    }\n  ],\n  \"shippingAddress\": {\n    \"name\": \"<PERSON>\",\n    \"street\": \"123 Main St, Apt 4B\",\n    \"city\": \"New York\",\n    \"state\": \"NY\",\n    \"zipCode\": \"10001\",\n    \"country\": \"USA\",\n    \"phone\": \"******-123-4567\"\n  },\n  \"notes\": \"Please deliver after 5 PM\"\n}"}}}, {"name": "Update Order", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/orders/:id", "path": ["orders", ":id"], "variable": [{"key": "id", "value": "", "description": "Order UUID"}]}, "body": {"mode": "raw", "raw": "{\n  \"status\": \"confirmed\",\n  \"trackingNumber\": \"TRK123456789\",\n  \"paymentMethod\": {\n    \"type\": \"credit_card\",\n    \"last4\": \"1234\",\n    \"brand\": \"visa\",\n    \"transactionId\": \"txn_123456\"\n  }\n}"}}}, {"name": "Cancel Order", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/orders/:id", "path": ["orders", ":id"], "variable": [{"key": "id", "value": "", "description": "Order UUID"}]}}}]}, {"name": "⚡ Catalog Management", "description": "Performance catalog and synchronization endpoints", "item": [{"name": "Get Catalog Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/catalog/products?page=1&limit=20&sortBy=createdAt&sortOrder=DESC", "path": ["catalog", "products"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "categoryId", "value": "", "disabled": true}, {"key": "vendorId", "value": "", "disabled": true}, {"key": "sortBy", "value": "createdAt"}, {"key": "sortOrder", "value": "DESC"}]}}}, {"name": "Search Catalog", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/catalog/products/search?q=laptop&page=1&limit=20", "path": ["catalog", "products", "search"], "query": [{"key": "q", "value": "laptop"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "Get Catalog Products by Vendor", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/catalog/products/vendor/:vendorId?page=1&limit=20", "path": ["catalog", "products", "vendor", ":vendorId"], "variable": [{"key": "vendorId", "value": ""}], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "Get Catalog Products by Category", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/catalog/products/category/:categoryId?page=1&limit=20", "path": ["catalog", "products", "category", ":categoryId"], "variable": [{"key": "categoryId", "value": ""}], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "Sync All Products to Catalog", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/catalog/sync/all", "path": ["catalog", "sync", "all"]}}}, {"name": "Get Sync Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/catalog/sync/stats", "path": ["catalog", "sync", "stats"]}}}, {"name": "Clear Catalog Cache", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/catalog/cache/clear", "path": ["catalog", "cache", "clear"]}}}]}, {"name": "Categories", "description": "Category management endpoints", "item": [{"name": "Get All Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/categories", "path": ["categories"]}}}, {"name": "Get Category by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/categories/:id", "path": ["categories", ":id"], "variable": [{"key": "id", "value": ""}]}}}, {"name": "Create Category", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/categories", "path": ["categories"]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Category Name\",\n  \"description\": \"Category Description\",\n  \"image\": \"category-image.jpg\"\n}"}}}, {"name": "Update Category", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/categories/:id", "path": ["categories", ":id"], "variable": [{"key": "id", "value": ""}]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Category Name\",\n  \"description\": \"Updated Category Description\"\n}"}}}, {"name": "Delete Category", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/categories/:id", "path": ["categories", ":id"], "variable": [{"key": "id", "value": ""}]}}}, {"name": "Get All Subcategories", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/categories/subcategories", "path": ["categories", "subcategories"]}}}, {"name": "Get Subcategory by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/categories/subcategories/:id", "path": ["categories", "subcategories", ":id"], "variable": [{"key": "id", "value": ""}]}}}, {"name": "Create Subcategory", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/categories/subcategories", "path": ["categories", "subcategories"]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Subcategory Name\",\n  \"description\": \"Subcategory Description\",\n  \"categoryId\": \"parent-category-uuid\",\n  \"image\": \"subcategory-image.jpg\"\n}"}}}, {"name": "Update Subcategory", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/categories/subcategories/:id", "path": ["categories", "subcategories", ":id"], "variable": [{"key": "id", "value": ""}]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Subcategory Name\",\n  \"description\": \"Updated Subcategory Description\"\n}"}}}, {"name": "Delete Subcategory", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/categories/subcategories/:id", "path": ["categories", "subcategories", ":id"], "variable": [{"key": "id", "value": ""}]}}}]}, {"name": "Tenants", "description": "Tenant management endpoints", "item": [{"name": "Get All Tenants", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/tenants", "path": ["tenants"]}}}, {"name": "Get Tenant by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/tenants/:id", "path": ["tenants", ":id"], "variable": [{"key": "id", "value": ""}]}}}]}, {"name": "Debug", "description": "Debug endpoints", "item": [{"name": "Get Tenant Info", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/debug/tenant", "path": ["debug", "tenant"]}}}]}]}