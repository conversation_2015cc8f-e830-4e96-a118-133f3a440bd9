import { <PERSON><PERSON><PERSON>, <PERSON>umn, PrimaryGenerated<PERSON><PERSON>umn, CreateDateColumn, UpdateDateColumn, OneToOne, JoinColumn } from 'typeorm';
import { UserRole } from '../../../common/interfaces/user-roles.enum';
import { Exclude } from 'class-transformer';
import { Vendor } from './vendor.entity';
import { Customer } from './customer.entity';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  email: string;

  @Column()
  @Exclude({ toPlainOnly: true })
  password: string;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.CUSTOMER,
  })
  role: UserRole;

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: 0 })
  loginAttempts: number;

  @Column({ nullable: true })
  lastLoginAttempt: Date;

  @Column({ nullable: true })
  refreshToken: string;

  @Column({ nullable: true })
  lastLoginIp: string;

  @Column({ nullable: true })
  lastLoginUserAgent: string;

  @Column({ nullable: true })
  lastRefreshTokenIp?: string;

  @Column({ nullable: true })
  lastRefreshTokenUserAgent?: string;

  @Column({ type: 'timestamp', nullable: true })
  lastRefreshTokenIssuedAt?: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToOne(() => Vendor, vendor => vendor.user, { nullable: true })
  vendor: Vendor;

  @OneToOne(() => Customer, customer => customer.user, { nullable: true })
  customer: Customer;
}
