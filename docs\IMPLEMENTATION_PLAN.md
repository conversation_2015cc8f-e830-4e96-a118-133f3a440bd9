# Implementation Plan for Performance-Optimized Multi-Tenant Architecture

## Phase 1: Immediate Performance Improvements (Week 1-2)

### 1.1 Redis Caching Implementation
```bash
# Install Redis dependencies
npm install redis @nestjs/cache-manager cache-manager-redis-store
```

### 1.2 Connection Pool Optimization
```typescript
// Update tenant connection configuration
export const getTenantDataSourceConfig = (databaseName: string): DataSourceOptions => ({
  // ... existing config
  extra: {
    max: 10,           // Reduced max connections per tenant
    min: 1,            // Minimum connections
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
  }
});
```

### 1.3 Database Indexing
```sql
-- Add indexes to existing tenant databases
CREATE INDEX CONCURRENTLY idx_products_active_created 
ON products(is_active, created_at DESC) WHERE is_active = true;

CREATE INDEX CONCURRENTLY idx_products_category_price 
ON products(category_id, price) WHERE is_active = true;

CREATE INDEX CONCURRENTLY idx_products_vendor_active 
ON products(vendor_id, is_active);
```

### 1.4 Pagination Implementation
```typescript
// Add pagination to ProductsService.findAll()
async findAll(
  tenantId?: string, 
  page: number = 1, 
  limit: number = 20,
  filters?: ProductFilters
): Promise<PaginatedProducts>
```

## Phase 2: Product Catalog Synchronization (Week 3-4)

### 2.1 Create Product Catalog Entity
```typescript
// src/modules/catalog/entities/product-catalog.entity.ts
@Entity('product_catalog')
export class ProductCatalog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  tenantProductId: string;

  @Column('uuid')
  vendorId: string;

  @Column()
  tenantSubdomain: string;

  @Column()
  name: string;

  @Column('text', { nullable: true })
  description: string;

  @Column('decimal', { precision: 10, scale: 2 })
  price: number;

  @Column('int')
  stock: number;

  @Column('text', { array: true, default: '{}' })
  images: string[];

  @Column('uuid', { nullable: true })
  categoryId: string;

  @Column('uuid', { nullable: true })
  subcategoryId: string;

  @Column({ default: true })
  isActive: boolean;

  @Index({ type: 'gin' })
  @Column('tsvector', { nullable: true })
  searchVector: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  syncedAt: Date;
}
```

### 2.2 Catalog Sync Service
```typescript
// src/modules/catalog/catalog-sync.service.ts
@Injectable()
export class CatalogSyncService {
  constructor(
    @InjectRepository(ProductCatalog)
    private catalogRepository: Repository<ProductCatalog>,
    private tenantsService: TenantsService,
    private cacheManager: Cache,
  ) {}

  async syncAllProducts(): Promise<void> {
    const vendors = await this.tenantsService.findAll();
    
    for (const vendor of vendors) {
      if (!vendor.isActive) continue;
      await this.syncVendorProducts(vendor);
    }
  }

  async syncVendorProducts(vendor: Vendor): Promise<void> {
    try {
      const tenantConnection = await this.tenantsService.getTenantConnection(vendor.subdomain);
      const productRepo = tenantConnection.dataSource.getRepository(Product);
      
      const products = await productRepo.find({
        where: { isActive: true },
        order: { updatedAt: 'DESC' }
      });

      for (const product of products) {
        await this.upsertCatalogProduct(product, vendor);
      }
    } catch (error) {
      console.error(`Sync failed for vendor ${vendor.subdomain}:`, error);
    }
  }

  private async upsertCatalogProduct(product: Product, vendor: Vendor): Promise<void> {
    const catalogProduct = await this.catalogRepository.findOne({
      where: {
        tenantSubdomain: vendor.subdomain,
        tenantProductId: product.id
      }
    });

    const catalogData = {
      tenantProductId: product.id,
      vendorId: vendor.id,
      tenantSubdomain: vendor.subdomain,
      name: product.name,
      description: product.description,
      price: product.price,
      stock: product.stock,
      images: product.images,
      categoryId: product.categoryId,
      isActive: product.isActive,
      searchVector: `${product.name} ${product.description}`,
      syncedAt: new Date()
    };

    if (catalogProduct) {
      await this.catalogRepository.update(catalogProduct.id, catalogData);
    } else {
      await this.catalogRepository.save(catalogData);
    }

    // Invalidate cache
    await this.cacheManager.del('products:all');
  }
}
```

### 2.3 Event-Driven Sync
```typescript
// src/modules/products/events/product.events.ts
export class ProductCreatedEvent {
  constructor(
    public readonly product: Product,
    public readonly tenantId: string,
    public readonly vendorId: string
  ) {}
}

export class ProductUpdatedEvent {
  constructor(
    public readonly product: Product,
    public readonly tenantId: string,
    public readonly vendorId: string
  ) {}
}

// src/modules/catalog/listeners/product-sync.listener.ts
@EventsHandler(ProductCreatedEvent, ProductUpdatedEvent)
export class ProductSyncListener implements IEventHandler {
  constructor(private catalogSyncService: CatalogSyncService) {}

  async handle(event: ProductCreatedEvent | ProductUpdatedEvent): Promise<void> {
    const vendor = await this.tenantsService.findBySubdomain(event.tenantId);
    await this.catalogSyncService.upsertCatalogProduct(event.product, vendor);
  }
}
```

## Phase 3: Optimized Product Service (Week 5)

### 3.1 Enhanced Products Service
```typescript
// src/modules/products/products.service.enhanced.ts
@Injectable()
export class ProductsServiceEnhanced {
  constructor(
    @InjectRepository(ProductCatalog)
    private catalogRepository: Repository<ProductCatalog>,
    private tenantsService: TenantsService,
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,
  ) {}

  async findAllFromCatalog(
    filters: ProductFilters = {},
    pagination: PaginationDto = { page: 1, limit: 20 }
  ): Promise<PaginatedResponse<ProductCatalog>> {
    const cacheKey = `products:${JSON.stringify({ filters, pagination })}`;
    
    // Try cache first
    const cached = await this.cacheManager.get(cacheKey);
    if (cached) return cached;

    const queryBuilder = this.catalogRepository
      .createQueryBuilder('pc')
      .leftJoinAndSelect('categories', 'c', 'pc.categoryId = c.id')
      .leftJoinAndSelect('vendors', 'v', 'pc.vendorId = v.id')
      .where('pc.isActive = :isActive', { isActive: true });

    // Apply filters
    if (filters.categoryId) {
      queryBuilder.andWhere('pc.categoryId = :categoryId', { categoryId: filters.categoryId });
    }
    
    if (filters.vendorId) {
      queryBuilder.andWhere('pc.vendorId = :vendorId', { vendorId: filters.vendorId });
    }
    
    if (filters.minPrice) {
      queryBuilder.andWhere('pc.price >= :minPrice', { minPrice: filters.minPrice });
    }
    
    if (filters.maxPrice) {
      queryBuilder.andWhere('pc.price <= :maxPrice', { maxPrice: filters.maxPrice });
    }
    
    if (filters.search) {
      queryBuilder.andWhere(
        'pc.searchVector @@ plainto_tsquery(:search)',
        { search: filters.search }
      );
    }

    // Apply sorting
    const sortField = filters.sortBy || 'createdAt';
    const sortOrder = filters.sortOrder || 'DESC';
    queryBuilder.orderBy(`pc.${sortField}`, sortOrder);

    // Apply pagination
    const offset = (pagination.page - 1) * pagination.limit;
    queryBuilder.skip(offset).take(pagination.limit);

    const [products, total] = await queryBuilder.getManyAndCount();

    const result = {
      data: products,
      total,
      page: pagination.page,
      limit: pagination.limit,
      totalPages: Math.ceil(total / pagination.limit)
    };

    // Cache for 5 minutes
    await this.cacheManager.set(cacheKey, result, 300);
    
    return result;
  }

  async searchProducts(query: string, pagination: PaginationDto): Promise<PaginatedResponse<ProductCatalog>> {
    const cacheKey = `search:${query}:${JSON.stringify(pagination)}`;
    
    const cached = await this.cacheManager.get(cacheKey);
    if (cached) return cached;

    const result = await this.catalogRepository
      .createQueryBuilder('pc')
      .leftJoinAndSelect('categories', 'c', 'pc.categoryId = c.id')
      .leftJoinAndSelect('vendors', 'v', 'pc.vendorId = v.id')
      .where('pc.isActive = :isActive', { isActive: true })
      .andWhere('pc.searchVector @@ plainto_tsquery(:query)', { query })
      .orderBy('ts_rank(pc.searchVector, plainto_tsquery(:query))', 'DESC')
      .skip((pagination.page - 1) * pagination.limit)
      .take(pagination.limit)
      .getManyAndCount();

    const response = {
      data: result[0],
      total: result[1],
      page: pagination.page,
      limit: pagination.limit,
      totalPages: Math.ceil(result[1] / pagination.limit)
    };

    // Cache search results for 2 minutes
    await this.cacheManager.set(cacheKey, response, 120);
    
    return response;
  }
}
```

## Phase 4: Customer Management Enhancement (Week 6)

### 4.1 Cross-Tenant Customer Orders
```typescript
// src/modules/orders/entities/customer-order.entity.ts
@Entity('customer_orders')
export class CustomerOrder {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  customerId: string;

  @Column('uuid')
  vendorId: string;

  @Column('uuid')
  tenantOrderId: string; // Reference to detailed order in tenant DB

  @Column('decimal', { precision: 10, scale: 2 })
  totalAmount: number;

  @Column()
  status: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
```

### 4.2 Customer Service Enhancement
```typescript
// Enhanced customer service for cross-tenant access
async getCustomerOrders(customerId: string): Promise<CustomerOrder[]> {
  return this.customerOrderRepository.find({
    where: { customerId },
    order: { createdAt: 'DESC' },
    relations: ['vendor']
  });
}

async getCustomerOrdersByVendor(customerId: string, vendorId: string): Promise<CustomerOrder[]> {
  return this.customerOrderRepository.find({
    where: { customerId, vendorId },
    order: { createdAt: 'DESC' }
  });
}
```

## Performance Monitoring

### Key Metrics to Track
1. **Response Times**
   - Product listing: < 100ms
   - Search queries: < 50ms
   - Product details: < 30ms

2. **Database Performance**
   - Connection pool utilization
   - Query execution times
   - Cache hit rates

3. **Sync Performance**
   - Sync lag time
   - Failed sync attempts
   - Data consistency checks

### Monitoring Implementation
```typescript
// Add performance monitoring middleware
@Injectable()
export class PerformanceMonitoringInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const start = Date.now();
    
    return next.handle().pipe(
      tap(() => {
        const duration = Date.now() - start;
        const request = context.switchToHttp().getRequest();
        console.log(`${request.method} ${request.url} - ${duration}ms`);
      })
    );
  }
}
```
