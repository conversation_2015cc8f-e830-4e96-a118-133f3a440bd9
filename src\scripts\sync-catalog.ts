import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { CatalogSyncService } from '../modules/catalog/services/catalog-sync.service';
import { Logger } from '@nestjs/common';

/**
 * <PERSON>ript to sync all existing products to the catalog
 * This should be run after implementing the catalog system to populate it with existing data
 * 
 * Usage:
 * npm run build
 * node dist/scripts/sync-catalog.js
 */

async function syncCatalog() {
  const logger = new Logger('CatalogSync');
  
  try {
    logger.log('Starting catalog sync script...');
    
    // Create NestJS application context
    const app = await NestFactory.createApplicationContext(AppModule);
    
    // Get the catalog sync service
    const catalogSyncService = app.get(CatalogSyncService);
    
    logger.log('Syncing all products to catalog...');
    const startTime = Date.now();
    
    // Sync all products from all tenants
    const result = await catalogSyncService.syncAllProducts();
    
    const duration = Date.now() - startTime;
    
    logger.log(`Catalog sync completed in ${duration}ms`);
    logger.log(`Results: ${result.success} successful, ${result.failed} failed, ${result.total} total`);
    
    if (result.failed > 0) {
      logger.warn(`${result.failed} products failed to sync. Check logs for details.`);
    }
    
    // Get sync statistics
    const stats = await catalogSyncService.getSyncStats();
    logger.log(`Catalog now contains ${stats.totalProducts} products`);
    logger.log(`Vendor breakdown:`);
    stats.vendorCounts.forEach(vc => {
      logger.log(`  - ${vc.subdomain}: ${vc.productCount} products`);
    });
    
    await app.close();
    
    logger.log('Catalog sync script completed successfully');
    process.exit(0);
    
  } catch (error) {
    logger.error('Catalog sync script failed:', error);
    process.exit(1);
  }
}

// Run the sync if this script is executed directly
if (require.main === module) {
  syncCatalog();
}

export { syncCatalog };
