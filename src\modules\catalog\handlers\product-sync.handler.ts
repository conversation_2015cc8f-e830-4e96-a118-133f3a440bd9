import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { Logger } from '@nestjs/common';
import { CatalogSyncService } from '../services/catalog-sync.service';
import { TenantsService } from '../../tenants/tenants.service';
import {
  ProductCreatedEvent,
  ProductUpdatedEvent,
  ProductDeletedEvent,
  ProductStatusChangedEvent,
} from '../../products/events/product.events';

@EventsHandler(ProductCreatedEvent)
export class ProductCreatedHandler implements IEventHandler<ProductCreatedEvent> {
  private readonly logger = new Logger(ProductCreatedHandler.name);

  constructor(
    private readonly catalogSyncService: CatalogSyncService,
    private readonly tenantsService: TenantsService,
  ) {}

  async handle(event: ProductCreatedEvent): Promise<void> {
    try {
      this.logger.debug(`Handling ProductCreatedEvent for product ${event.product.id}`);

      const vendor = await this.tenantsService.findBySubdomain(event.tenantSubdomain);
      if (!vendor) {
        this.logger.error(`Vendor not found for subdomain: ${event.tenantSubdomain}`);
        return;
      }

      await this.catalogSyncService.upsertCatalogProduct(event.product, vendor);

      this.logger.debug(`Successfully synced new product ${event.product.id} to catalog`);
    } catch (error) {
      this.logger.error(`Failed to sync new product ${event.product.id} to catalog:`, error);
    }
  }
}

@EventsHandler(ProductUpdatedEvent)
export class ProductUpdatedHandler implements IEventHandler<ProductUpdatedEvent> {
  private readonly logger = new Logger(ProductUpdatedHandler.name);

  constructor(
    private readonly catalogSyncService: CatalogSyncService,
    private readonly tenantsService: TenantsService,
  ) {}

  async handle(event: ProductUpdatedEvent): Promise<void> {
    try {
      this.logger.debug(`Handling ProductUpdatedEvent for product ${event.product.id}`);

      const vendor = await this.tenantsService.findBySubdomain(event.tenantSubdomain);
      if (!vendor) {
        this.logger.error(`Vendor not found for subdomain: ${event.tenantSubdomain}`);
        return;
      }

      await this.catalogSyncService.upsertCatalogProduct(event.product, vendor);

      this.logger.debug(`Successfully synced updated product ${event.product.id} to catalog`);
    } catch (error) {
      this.logger.error(`Failed to sync updated product ${event.product.id} to catalog:`, error);
    }
  }
}

@EventsHandler(ProductDeletedEvent)
export class ProductDeletedHandler implements IEventHandler<ProductDeletedEvent> {
  private readonly logger = new Logger(ProductDeletedHandler.name);

  constructor(
    private readonly catalogSyncService: CatalogSyncService,
  ) {}

  async handle(event: ProductDeletedEvent): Promise<void> {
    try {
      this.logger.debug(`Handling ProductDeletedEvent for product ${event.productId}`);

      await this.catalogSyncService.removeFromCatalog(event.productId, event.tenantSubdomain);

      this.logger.debug(`Successfully removed product ${event.productId} from catalog`);
    } catch (error) {
      this.logger.error(`Failed to remove product ${event.productId} from catalog:`, error);
    }
  }
}

@EventsHandler(ProductStatusChangedEvent)
export class ProductStatusChangedHandler implements IEventHandler<ProductStatusChangedEvent> {
  private readonly logger = new Logger(ProductStatusChangedHandler.name);

  constructor(
    private readonly catalogSyncService: CatalogSyncService,
  ) {}

  async handle(event: ProductStatusChangedEvent): Promise<void> {
    try {
      this.logger.debug(`Handling ProductStatusChangedEvent for product ${event.productId}`);

      await this.catalogSyncService.updateProductStatus(
        event.productId,
        event.tenantSubdomain,
        event.isActive
      );

      this.logger.debug(`Successfully updated product ${event.productId} status in catalog`);
    } catch (error) {
      this.logger.error(`Failed to update product ${event.productId} status in catalog:`, error);
    }
  }
}
