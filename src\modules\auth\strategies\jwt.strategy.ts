import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { UsersService } from '../../users/users.service';
import { JwtPayload } from '../interfaces/jwt-payload.interface';
import { AuthRequest } from '../interfaces/auth-request.interface';
import { UserRole } from '../../../common/interfaces/user-roles.enum';
import { Request } from 'express';

interface RequestWithCookies extends Omit<Request, 'cookies'> {
  cookies?: {
    Authentication?: string;
    [key: string]: string | undefined;
  };
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private configService: ConfigService,
    private usersService: UsersService,
  ) {
    const secretKey = configService.get<string>('JWT_SECRET');
    if (!secretKey) {
      throw new Error('JWT_SECRET is not defined in the environment variables');
    }

    super({
      jwtFromRequest: ExtractJwt.fromExtractors([
        ExtractJwt.fromAuthHeaderAsBearerToken(),
        (request: Request) => {
          const req = request as RequestWithCookies;
          const token = req.cookies?.Authentication;
          return token || null;
        },
      ]),
      ignoreExpiration: false,
      secretOrKey: secretKey,
      passReqToCallback: true,
    });
  }

  async validate(
    request: AuthRequest,
    payload: JwtPayload,
  ): Promise<{ id: string; email: string; role: UserRole }> {
    try {
      const user = await this.usersService.findOneById(payload.sub);

      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      if (!user.isActive) {
        throw new UnauthorizedException('User is inactive');
      }

      // Check if user's role matches the token's role
      if (user.role !== payload.role) {
        throw new UnauthorizedException('Invalid role in token');
      }

      // Verify IP address matches (if available)
      const currentIp = request.ip;
      if (payload.ip && currentIp && payload.ip !== currentIp) {
        throw new UnauthorizedException('Invalid token: IP mismatch');
      }

      // Verify user agent matches (if available)
      const currentUserAgent = request.headers['user-agent'];
      if (
        payload.userAgent &&
        currentUserAgent &&
        payload.userAgent !== currentUserAgent
      ) {
        throw new UnauthorizedException('Invalid token: User agent mismatch');
      }

      // Add request metadata
      request.userIp = request.ip;
      request.userAgent = request.headers['user-agent'] || undefined;
      request.user = {
        id: user.id,
        email: user.email,
        role: user.role,
      };

      return {
        id: user.id,
        email: user.email,
        role: user.role,
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new UnauthorizedException('Invalid token');
    }
  }
}
