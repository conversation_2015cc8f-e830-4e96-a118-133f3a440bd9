import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Req, HttpCode, UnauthorizedException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ProductsService } from './products.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { UserRole } from '../../common/interfaces/user-roles.enum';
import { TenantRequest } from '../../common/interfaces/tenant-request.interface';
import { User } from '../users/entities/user.entity';

interface AuthenticatedRequest extends TenantRequest {
  user: User & { id: string };
}

interface AuthenticatedTenantRequest extends AuthenticatedRequest {
  tenantId: string;
}

@ApiTags('products')
@Controller('products')
export class ProductsController {
  constructor(private readonly productsService: ProductsService) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.VENDOR)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Create a new product' })
  @ApiResponse({ status: 201, description: 'Product created successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - requires vendor role' })
  async create(
    @Body() createProductDto: CreateProductDto,
    @Req() req: AuthenticatedRequest,
  ) {
    if (!req.tenantId) {
      throw new UnauthorizedException('Tenant ID is required');
    }
    return this.productsService.create(
      createProductDto, 
      req.tenantId,
      req.user.id
    );
  }

  @Get()
  @ApiOperation({ summary: 'Get all products' })
  @ApiResponse({ status: 200, description: 'Returns all products' })
  findAll(@Req() req: TenantRequest) {
    // tenantId is optional for findAll, if not provided returns all products across tenants
    return this.productsService.findAll(req.tenantId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get product by ID' })
  @ApiResponse({ status: 200, description: 'Returns the product' })
  @ApiResponse({ status: 404, description: 'Product not found' })
  findOne(@Param('id') id: string, @Req() req: TenantRequest) {
    if (!req.tenantId) {
      throw new UnauthorizedException('Tenant ID is required');
    }
    return this.productsService.findOne(id, req.tenantId);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.VENDOR)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Update a product' })
  @ApiResponse({ status: 200, description: 'Product updated successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - requires vendor role' })
  @ApiResponse({ status: 404, description: 'Product not found' })
  async update(
    @Param('id') id: string,
    @Body() updateProductDto: UpdateProductDto,
    @Req() req: AuthenticatedRequest,
  ) {
    if (!req.tenantId) {
      throw new UnauthorizedException('Tenant ID is required');
    }
    return this.productsService.update(
      id,
      updateProductDto,
      req.tenantId,
      req.user.id
    );
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.VENDOR)
  @ApiBearerAuth('JWT-auth')
  @HttpCode(204)
  @ApiOperation({ summary: 'Delete a product' })
  @ApiResponse({ status: 204, description: 'Product deleted successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - requires vendor role' })
  @ApiResponse({ status: 404, description: 'Product not found' })
  async remove(@Param('id') id: string, @Req() req: AuthenticatedRequest) {
    if (!req.tenantId) {
      throw new UnauthorizedException('Tenant ID is required');
    }
    await this.productsService.remove(id, req.tenantId, req.user.id);
  }
}
