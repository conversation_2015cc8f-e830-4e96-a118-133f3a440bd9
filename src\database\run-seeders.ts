import { NestFactory } from '@nestjs/core';
import { DatabaseSeederModule } from './database-seeder.module';
import { MainSeeder } from './seeders/main/main.seeder';

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(DatabaseSeederModule);

  try {
    const seeder = app.get(MainSeeder);
    await seeder.seed();
    console.log('Database seeding completed successfully');
  } catch (error) {
    console.error('Error during database seeding:', error);
    throw error;
  } finally {
    await app.close();
  }
}

bootstrap();
