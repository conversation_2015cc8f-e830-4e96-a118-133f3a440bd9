import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGenerated<PERSON><PERSON>umn,
  <PERSON>To<PERSON>ne,
  Jo<PERSON><PERSON><PERSON>um<PERSON>,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Order } from './order.entity';

@Entity('order_items')
export class OrderItem {
  @ApiProperty({
    description: 'Unique identifier for the order item',
    example: '3cbd87ba-7a13-4abf-aafa-c72bb2b7eeb6',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'Order ID this item belongs to',
    example: '3cbd87ba-7a13-4abf-aafa-c72bb2b7eeb6',
  })
  @Column('uuid')
  orderId: string;

  @ApiProperty({
    description: 'Product ID from tenant database',
    example: '3cbd87ba-7a13-4abf-aafa-c72bb2b7eeb6',
  })
  @Column('uuid')
  productId: string;

  @ApiProperty({
    description: 'Product variant ID if applicable',
    example: '3cbd87ba-7a13-4abf-aafa-c72bb2b7eeb6',
    required: false,
  })
  @Column('uuid', { nullable: true })
  productVariantId: string;

  @ApiProperty({
    description: 'Product name at time of order',
    example: 'Gaming Laptop',
  })
  @Column()
  productName: string;

  @ApiProperty({
    description: 'Product SKU at time of order',
    example: 'LAPTOP-001',
    required: false,
  })
  @Column({ nullable: true })
  productSku: string;

  @ApiProperty({
    description: 'Product price at time of order',
    example: 1299.99,
  })
  @Column('decimal', { precision: 12, scale: 2 })
  unitPrice: number;

  @ApiProperty({
    description: 'Quantity ordered',
    example: 2,
  })
  @Column('int')
  quantity: number;

  @ApiProperty({
    description: 'Total price for this line item (unitPrice * quantity)',
    example: 2599.98,
  })
  @Column('decimal', { precision: 12, scale: 2 })
  totalPrice: number;

  @ApiProperty({
    description: 'Product attributes at time of order',
    example: { color: 'Black', size: '15 inch', warranty: '2 years' },
    required: false,
  })
  @Column('jsonb', { nullable: true })
  productAttributes: Record<string, any>;

  @ApiProperty({
    description: 'Product image URL at time of order',
    example: 'https://example.com/laptop.jpg',
    required: false,
  })
  @Column({ nullable: true })
  productImage?: string;

  // Relations
  @ManyToOne(() => Order, (order) => order.items, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'orderId' })
  order: Order;
}
