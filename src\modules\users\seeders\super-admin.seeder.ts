import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { User } from '../entities/user.entity';
import { UserRole } from '../../../common/interfaces/user-roles.enum';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class SuperAdminSeeder {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    private configService: ConfigService,
  ) {}

  async seed(): Promise<void> {
    try {
      // Check if super admin already exists
      const existingSuperAdmin = await this.usersRepository.findOne({
        where: { role: UserRole.SUPER_ADMIN },
      });

      if (existingSuperAdmin) {
        console.log('Super admin already exists, skipping seeder');
        return;
      }

      // Create super admin user
      const email = this.configService.get<string>('SUPER_ADMIN_EMAIL', '<EMAIL>');
      const password = this.configService.get<string>('SUPER_ADMIN_PASSWORD', 'Admin@123');

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 10);

      // Create user
      const superAdmin = this.usersRepository.create({
        email,
        password: hashedPassword,
        role: UserRole.SUPER_ADMIN,
      });

      await this.usersRepository.save(superAdmin);

      console.log(`Super admin created with email: ${email}`);
    } catch (error) {
      console.error(`Error seeding super admin: ${error.message}`);
      // Don't throw the error to allow the application to start
    }
  }
}
