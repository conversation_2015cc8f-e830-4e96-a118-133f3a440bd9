# Combined Optimal Multi-Tenant E-commerce Schema

## 🏆 **Best-of-All-Agents Database Design**

This schema combines the best elements from ChatGPT, DeepSeek, Claude, and Gemini recommendations.

## 📊 **Architecture Overview**

```
┌─────────────────────────────────────────────────────────────────┐
│                        MAIN DATABASE                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │   TENANTS   │ │ CATEGORIES  │ │  CUSTOMERS  │ │ SUPER_ADMIN │ │
│  │  (Vendors)  │ │(Hierarchical)│ │  (Shared)   │ │             │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                 PRODUCT_CATALOG                             │ │
│  │           (Synchronized Product Index)                     │ │
│  │  • Fast cross-tenant search & listing                     │ │
│  │  • Full-text search with tsvector                        │ │
│  │  • Redis caching layer                                   │ │
│  │  • Real-time sync from tenant DBs                       │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │              CROSS-TENANT FEATURES                         │ │
│  │  • Customer tenant profiles                               │ │
│  │  • Cross-tenant orders                                   │ │
│  │  • Global analytics                                      │ │
│  │  • Sync status tracking                                 │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│  TENANT DB 1    │  │  TENANT DB 2    │  │  TENANT DB N    │
│ ┌─────────────┐ │  │ ┌─────────────┐ │  │ ┌─────────────┐ │
│ │  PRODUCTS   │ │  │ │  PRODUCTS   │ │  │ │  PRODUCTS   │ │
│ │ (Master +   │ │  │ │ (Master +   │ │  │ │ (Master +   │ │
│ │  Variants)  │ │  │ │  Variants)  │ │  │ │  Variants)  │ │
│ └─────────────┘ │  │ └─────────────┘ │  │ └─────────────┘ │
│ ┌─────────────┐ │  │ ┌─────────────┐ │  │ ┌─────────────┐ │
│ │   ORDERS    │ │  │ │   ORDERS    │ │  │ │   ORDERS    │ │
│ │ (Detailed)  │ │  │ │ (Detailed)  │ │  │ │ (Detailed)  │ │
│ └─────────────┘ │  │ └─────────────┘ │  │ └─────────────┘ │
│ ┌─────────────┐ │  │ ┌─────────────┐ │  │ ┌─────────────┐ │
│ │ INVENTORY + │ │  │ │ INVENTORY + │ │  │ │ INVENTORY + │ │
│ │   CARTS     │ │  │ │   CARTS     │ │  │ │   CARTS     │ │
│ └─────────────┘ │  │ └─────────────┘ │  │ └─────────────┘ │
└─────────────────┘  └─────────────────┘  └─────────────────┘
```

## 🗄️ **MAIN DATABASE SCHEMA**

### **Core Extensions**
```sql
-- Enable necessary PostgreSQL extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";
CREATE EXTENSION IF NOT EXISTS "btree_gin";
```

### **1. Tenant Management** (Best from DeepSeek + Claude)
```sql
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    subdomain VARCHAR(100) UNIQUE NOT NULL,
    database_name VARCHAR(100) NOT NULL UNIQUE,

    -- Contact & Business Info
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    description TEXT,
    logo_url VARCHAR(500),

    -- Status & Subscription (Claude's enhancement)
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended', 'pending')),
    subscription_plan VARCHAR(50) DEFAULT 'basic',
    subscription_expires_at TIMESTAMP WITH TIME ZONE,

    -- Settings (DeepSeek's JSONB approach)
    settings JSONB DEFAULT '{}',

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_tenants_subdomain ON tenants(subdomain);
CREATE INDEX idx_tenants_status ON tenants(status) WHERE status = 'active';
CREATE INDEX idx_tenants_subscription ON tenants(subscription_expires_at) WHERE subscription_expires_at IS NOT NULL;
```

### **2. Hierarchical Categories** (Best from Claude + DeepSeek)
```sql
CREATE TABLE categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,

    -- Hierarchical structure (Claude's approach)
    parent_id UUID REFERENCES categories(id) ON DELETE CASCADE,

    -- Display & SEO (Claude's enhancement)
    image_url VARCHAR(500),
    sort_order INTEGER DEFAULT 0,
    meta_title VARCHAR(255),
    meta_description TEXT,

    -- Status
    is_active BOOLEAN DEFAULT true,

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for hierarchical queries
CREATE INDEX idx_categories_parent_id ON categories(parent_id);
CREATE INDEX idx_categories_slug ON categories(slug);
CREATE INDEX idx_categories_active_sort ON categories(is_active, sort_order) WHERE is_active = true;

-- Materialized path for fast hierarchy queries (Claude's optimization)
ALTER TABLE categories ADD COLUMN path TEXT;
CREATE INDEX idx_categories_path ON categories USING GIN(path gin_trgm_ops);
```

### **3. Shared Customer Management** (Best from Claude + Gemini)
```sql
CREATE TABLE customers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,

    -- Personal Info
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    date_of_birth DATE,
    gender VARCHAR(10) CHECK (gender IN ('male', 'female', 'other')),
    avatar_url VARCHAR(500),

    -- Account Status
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    email_verified_at TIMESTAMP WITH TIME ZONE,

    -- Registration tracking (Gemini's enhancement)
    registered_from_tenant_id UUID REFERENCES tenants(id) ON DELETE SET NULL,

    -- Activity tracking
    last_login_at TIMESTAMP WITH TIME ZONE,

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Customer addresses (Claude's comprehensive approach)
CREATE TABLE customer_addresses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    type VARCHAR(20) DEFAULT 'shipping' CHECK (type IN ('billing', 'shipping')),

    -- Address details
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    company VARCHAR(255),
    address_line_1 VARCHAR(255) NOT NULL,
    address_line_2 VARCHAR(255),
    city VARCHAR(100) NOT NULL,
    state VARCHAR(100) NOT NULL,
    postal_code VARCHAR(20) NOT NULL,
    country VARCHAR(100) NOT NULL,
    phone VARCHAR(20),

    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Customer tenant profiles for personalization (Claude's innovation)
CREATE TABLE customer_tenant_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,

    -- Personalization data
    preferences JSONB DEFAULT '{}',
    loyalty_points INTEGER DEFAULT 0,

    -- Analytics
    total_orders INTEGER DEFAULT 0,
    total_spent DECIMAL(12,2) DEFAULT 0.00,
    first_order_at TIMESTAMP WITH TIME ZONE,
    last_order_at TIMESTAMP WITH TIME ZONE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(customer_id, tenant_id)
);

-- Indexes for customer queries
CREATE INDEX idx_customers_email ON customers(email);
CREATE INDEX idx_customers_active ON customers(is_active) WHERE is_active = true;
CREATE INDEX idx_customer_addresses_customer_id ON customer_addresses(customer_id);
CREATE INDEX idx_customer_tenant_profiles_composite ON customer_tenant_profiles(customer_id, tenant_id);
```

### **4. Super Admin Management**
```sql
CREATE TABLE super_admins (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),

    -- Permissions
    permissions JSONB DEFAULT '{}',

    -- Status
    is_active BOOLEAN DEFAULT true,
    last_login_at TIMESTAMP WITH TIME ZONE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_super_admins_email ON super_admins(email);
```

### **5. Product Catalog (Performance Core)** (Best from ChatGPT + Gemini + Claude)
```sql
-- Main product catalog for fast cross-tenant queries
CREATE TABLE product_catalog (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

    -- Tenant reference
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    tenant_product_id UUID NOT NULL, -- Original product ID in tenant DB

    -- Core product data (ChatGPT + Gemini approach)
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    description TEXT,
    short_description TEXT,

    -- Pricing
    price NUMERIC(12,2) NOT NULL,
    compare_price NUMERIC(12,2),
    cost_price NUMERIC(12,2),

    -- Categorization
    category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    category_path TEXT, -- Cached hierarchy for fast filtering
    brand VARCHAR(100),

    -- Inventory (simplified for listing)
    stock_quantity INTEGER DEFAULT 0,
    stock_status VARCHAR(20) DEFAULT 'in_stock' CHECK (stock_status IN ('in_stock', 'low_stock', 'out_of_stock')),

    -- Media & Attributes (DeepSeek's JSONB approach)
    images JSONB DEFAULT '[]',
    attributes JSONB DEFAULT '{}',
    tags TEXT[],

    -- SEO & Marketing
    sku VARCHAR(100),
    featured BOOLEAN DEFAULT false,

    -- Reviews & Ratings (Claude's enhancement)
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    review_count INTEGER DEFAULT 0,

    -- Status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'draft')),
    is_published BOOLEAN DEFAULT true,

    -- Search optimization (ChatGPT's full-text search)
    search_vector TSVECTOR,

    -- Sync tracking
    last_synced_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    UNIQUE(tenant_id, tenant_product_id)
);

-- Performance indexes (Combined best practices)
CREATE INDEX idx_product_catalog_tenant ON product_catalog(tenant_id);
CREATE INDEX idx_product_catalog_category ON product_catalog(category_id);
CREATE INDEX idx_product_catalog_status ON product_catalog(status) WHERE status = 'active';
CREATE INDEX idx_product_catalog_featured ON product_catalog(featured) WHERE featured = true;
CREATE INDEX idx_product_catalog_price ON product_catalog(price);
CREATE INDEX idx_product_catalog_rating ON product_catalog(average_rating DESC);
CREATE INDEX idx_product_catalog_created ON product_catalog(created_at DESC);

-- Full-text search index (ChatGPT's approach)
CREATE INDEX idx_product_catalog_search ON product_catalog USING GIN(search_vector);

-- Composite indexes for common queries
CREATE INDEX idx_product_catalog_tenant_category_status ON product_catalog(tenant_id, category_id, status);
CREATE INDEX idx_product_catalog_category_price ON product_catalog(category_id, price) WHERE status = 'active';

-- Tags search (DeepSeek's approach)
CREATE INDEX idx_product_catalog_tags ON product_catalog USING GIN(tags);

-- Trigger for automatic search vector updates
CREATE OR REPLACE FUNCTION update_product_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := to_tsvector('english',
        COALESCE(NEW.name, '') || ' ' ||
        COALESCE(NEW.description, '') || ' ' ||
        COALESCE(NEW.brand, '') || ' ' ||
        COALESCE(NEW.category_path, '') || ' ' ||
        COALESCE(array_to_string(NEW.tags, ' '), '')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_product_catalog_search_vector
    BEFORE INSERT OR UPDATE ON product_catalog
    FOR EACH ROW EXECUTE FUNCTION update_product_search_vector();
```

### **6. Cross-Tenant Order Management** (Claude's Innovation)
```sql
-- Cross-tenant orders for customers shopping from multiple vendors
CREATE TABLE cross_tenant_orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE RESTRICT,

    -- Order identification
    order_number VARCHAR(50) UNIQUE NOT NULL,

    -- Financial summary
    subtotal DECIMAL(12,2) NOT NULL,
    tax_amount DECIMAL(12,2) DEFAULT 0.00,
    shipping_amount DECIMAL(12,2) DEFAULT 0.00,
    discount_amount DECIMAL(12,2) DEFAULT 0.00,
    total_amount DECIMAL(12,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',

    -- Status tracking
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded')),
    payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded', 'partially_refunded')),

    -- Address information
    shipping_address JSONB NOT NULL,
    billing_address JSONB NOT NULL,

    -- Tenant order references (JSONB for flexibility)
    tenant_orders JSONB NOT NULL, -- Array of {tenant_id, tenant_order_id, amount}

    -- Additional info
    notes TEXT,

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for cross-tenant orders
CREATE INDEX idx_cross_tenant_orders_customer ON cross_tenant_orders(customer_id);
CREATE INDEX idx_cross_tenant_orders_status ON cross_tenant_orders(status);
CREATE INDEX idx_cross_tenant_orders_payment_status ON cross_tenant_orders(payment_status);
CREATE INDEX idx_cross_tenant_orders_created ON cross_tenant_orders(created_at DESC);
CREATE INDEX idx_cross_tenant_orders_number ON cross_tenant_orders(order_number);
```

### **7. Sync Status Tracking** (Claude's Monitoring)
```sql
-- Track synchronization status between main and tenant databases
CREATE TABLE sync_status (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    entity_type VARCHAR(50) NOT NULL, -- 'products', 'orders', 'inventory'

    -- Sync tracking
    last_sync_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(20) DEFAULT 'success' CHECK (status IN ('success', 'failed', 'in_progress')),
    error_message TEXT,
    records_synced INTEGER DEFAULT 0,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(tenant_id, entity_type)
);

CREATE INDEX idx_sync_status_tenant_entity ON sync_status(tenant_id, entity_type);
CREATE INDEX idx_sync_status_failed ON sync_status(status) WHERE status = 'failed';
```
```
