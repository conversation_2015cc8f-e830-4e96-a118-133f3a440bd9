import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { User } from '../modules/users/entities/user.entity';
import { Customer } from '../modules/users/entities/customer.entity';
import { Vendor } from '../modules/users/entities/vendor.entity';
import { Category } from '../modules/categories/entities/category.entity';
import { Subcategory } from '../modules/categories/entities/subcategory.entity';
import { MainSeeder } from './seeders/main/main.seeder';
import { UsersSeeder } from './seeders/main/users.seeder';
import { CategoriesSeeder } from './seeders/main/categories.seeder';
import { SubcategoriesSeeder } from './seeders/main/subcategories.seeder';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get<string>('DB_HOST') || 'localhost',
        port: parseInt(configService.get<string>('DB_PORT') || '5432', 10),
        username: configService.get<string>('DB_USERNAME') || 'postgres',
        password: configService.get<string>('DB_PASSWORD') || 'postgres',
        database: configService.get<string>('DB_MAIN_DATABASE') || 'postgres',
        entities: [__dirname + '/../**/*.entity{.ts,.js}'],
        synchronize: false,
        logging: true,
        autoLoadEntities: true,
      }),
    }),
    TypeOrmModule.forFeature([User, Customer, Vendor, Category, Subcategory]),
  ],
  providers: [
    MainSeeder,
    UsersSeeder,
    CategoriesSeeder,
    SubcategoriesSeeder,
  ],
  exports: [MainSeeder],
})
export class DatabaseSeederModule {}
