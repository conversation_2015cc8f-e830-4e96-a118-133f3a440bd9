import { DataSource } from 'typeorm';
import { Client } from 'pg';
import * as dotenv from 'dotenv';
import { getTenantDataSourceConfig } from './tenant-data-source';

dotenv.config();

/**
 * Multi-Tenant Migration Runner
 * 
 * This utility handles running migrations across all tenant databases.
 * It's separate from the main tenant-data-source.ts to keep that file clean
 * and focused on TypeORM CLI operations.
 */

/**
 * Gets the PostgreSQL client configuration for connecting to databases
 * @param databaseName Optional database name (defaults to main database)
 * @returns Client configuration
 */
const getPgClientConfig = (databaseName?: string) => ({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT ? parseInt(process.env.DB_PORT, 10) : 5432,
  user: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  database: databaseName || process.env.DB_MAIN_DATABASE || 'postgres',
});

/**
 * Gets all tenant database names from the main database
 */
export async function getTenantDatabases(): Promise<Array<{ databaseName: string; subdomain: string; storeName: string }>> {
  const mainClient = new Client(getPgClientConfig());
  
  try {
    await mainClient.connect();
    const res = await mainClient.query(
      'SELECT "databaseName", "subdomain", "storeName" FROM vendors WHERE "databaseName" IS NOT NULL ORDER BY "storeName"'
    );
    return res.rows;
  } catch (error) {
    console.error('Error fetching tenant databases:', error);
    return [];
  } finally {
    await mainClient.end();
  }
}

/**
 * Checks if a tenant database has pending migrations
 */
async function hasPendingMigrations(databaseName: string): Promise<boolean> {
  const dataSource = new DataSource(getTenantDataSourceConfig(databaseName));
  
  try {
    await dataSource.initialize();
    const pendingMigrations = await dataSource.showMigrations();
    return pendingMigrations;
  } catch (error) {
    console.error(`Error checking migrations for ${databaseName}:`, error);
    return false;
  } finally {
    await dataSource.destroy();
  }
}

/**
 * Runs migrations for a specific tenant database
 */
async function runMigrationsForTenant(databaseName: string): Promise<boolean> {
  const dataSource = new DataSource(getTenantDataSourceConfig(databaseName));
  
  try {
    await dataSource.initialize();
    const migrations = await dataSource.runMigrations();
    
    if (migrations.length === 0) {
      console.log(`  ✓ No pending migrations for ${databaseName}`);
    } else {
      console.log(`  ✓ Applied ${migrations.length} migration(s) to ${databaseName}`);
      migrations.forEach(migration => {
        console.log(`    - ${migration.name}`);
      });
    }
    
    return true;
  } catch (error) {
    console.error(`  ✗ Migration failed for ${databaseName}:`, error.message);
    return false;
  } finally {
    await dataSource.destroy();
  }
}

/**
 * Migrates all tenant databases
 */
export async function migrateAllTenants(): Promise<void> {
  console.log('🚀 Starting migrations for all tenant databases...\n');

  const tenants = await getTenantDatabases();
  
  if (tenants.length === 0) {
    console.log('No tenant databases found to migrate.');
    return;
  }

  console.log(`Found ${tenants.length} tenant database(s):\n`);
  
  let successCount = 0;
  let failureCount = 0;

  for (const tenant of tenants) {
    console.log(`🔄 Processing: ${tenant.storeName} (${tenant.databaseName})`);
    
    const success = await runMigrationsForTenant(tenant.databaseName);
    
    if (success) {
      successCount++;
    } else {
      failureCount++;
    }
    
    console.log(''); // Empty line for readability
  }

  console.log('📊 Migration Summary:');
  console.log(`  ✅ Successful: ${successCount}`);
  console.log(`  ❌ Failed: ${failureCount}`);
  console.log(`  📋 Total: ${tenants.length}`);
  
  if (failureCount > 0) {
    console.log('\n⚠️  Some migrations failed. Please check the errors above.');
    process.exit(1);
  } else {
    console.log('\n🎉 All tenant migrations completed successfully!');
  }
}

/**
 * CLI entry point
 */
export async function runTenantMigrations(): Promise<void> {
  try {
    await migrateAllTenants();
    process.exit(0);
  } catch (error) {
    console.error('❌ Tenant migration runner failed:', error);
    process.exit(1);
  }
}

// Execute if run directly
if (require.main === module) {
  runTenantMigrations();
}
