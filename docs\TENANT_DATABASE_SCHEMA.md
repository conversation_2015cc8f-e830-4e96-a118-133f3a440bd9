# Tenant Database Schema (Per Vendor)

## 🏪 **Individual Tenant Database Design**

Each vendor gets their own dedicated PostgreSQL database with this optimized schema.

### **Core Extensions**
```sql
-- Enable necessary PostgreSQL extensions for each tenant DB
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
```

## 📦 **Product Management** (Best from Claude + DeepSeek)

### **1. Products (Master Data)**
```sql
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

    -- Basic product info
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    short_description TEXT,

    -- Pricing
    price NUMERIC(12,2) NOT NULL,
    compare_price NUMERIC(12,2), -- MSRP or original price
    cost_price NUMERIC(12,2),    -- Vendor's cost

    -- Categorization (references main database)
    category_id UUID NOT NULL, -- References main.categories (no FK constraint)
    subcategory_id UUID,       -- References main.categories (no FK constraint)

    -- Product details
    brand VARCHAR(100),
    sku VARCHAR(100) UNIQUE,
    barcode VARCHAR(100),
    weight DECIMAL(8,2),
    dimensions JSONB, -- {length, width, height, unit}

    -- Inventory tracking
    track_inventory BOOLEAN DEFAULT true,
    stock_quantity INTEGER DEFAULT 0,
    low_stock_threshold INTEGER DEFAULT 5,

    -- Product attributes (DeepSeek's flexible approach)
    attributes JSONB DEFAULT '{}', -- Color, Size, Material, etc.
    tags TEXT[],

    -- Media
    images JSONB DEFAULT '[]', -- Array of image objects
    videos JSONB DEFAULT '[]', -- Array of video objects

    -- Status & Publishing
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('active', 'inactive', 'draft')),
    is_published BOOLEAN DEFAULT false,
    published_at TIMESTAMP WITH TIME ZONE,
    featured BOOLEAN DEFAULT false,

    -- Shipping & Tax
    requires_shipping BOOLEAN DEFAULT true,
    taxable BOOLEAN DEFAULT true,
    tax_category VARCHAR(50),

    -- SEO
    meta_title VARCHAR(255),
    meta_description TEXT,

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Product indexes for performance
CREATE INDEX idx_products_category ON products(category_id);
CREATE INDEX idx_products_status_published ON products(status, is_published) WHERE status = 'active';
CREATE INDEX idx_products_slug ON products(slug);
CREATE INDEX idx_products_sku ON products(sku);
CREATE INDEX idx_products_featured ON products(featured) WHERE featured = true;
CREATE INDEX idx_products_price ON products(price);
CREATE INDEX idx_products_stock ON products(stock_quantity);
CREATE INDEX idx_products_updated ON products(updated_at DESC);
CREATE INDEX idx_products_tags ON products USING GIN(tags);
CREATE INDEX idx_products_attributes ON products USING GIN(attributes);
```

### **2. Product Variants** (DeepSeek + Claude Enhancement)
```sql
CREATE TABLE product_variants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,

    -- Variant identification
    name VARCHAR(255), -- e.g., "Red Large T-Shirt"
    sku VARCHAR(100) UNIQUE,
    barcode VARCHAR(100),

    -- Pricing (can override product price)
    price NUMERIC(12,2),
    compare_price NUMERIC(12,2),
    cost_price NUMERIC(12,2),

    -- Physical properties
    weight DECIMAL(8,2),
    dimensions JSONB,

    -- Variant options (up to 3 option types)
    option1_name VARCHAR(50),  -- e.g., "Size"
    option1_value VARCHAR(100), -- e.g., "Large"
    option2_name VARCHAR(50),   -- e.g., "Color"
    option2_value VARCHAR(100), -- e.g., "Red"
    option3_name VARCHAR(50),   -- e.g., "Material"
    option3_value VARCHAR(100), -- e.g., "Cotton"

    -- Inventory
    inventory_quantity INTEGER DEFAULT 0,
    inventory_policy VARCHAR(20) DEFAULT 'deny' CHECK (inventory_policy IN ('deny', 'continue')),

    -- Display
    position INTEGER DEFAULT 0,
    image_url VARCHAR(500),

    -- Shipping & Tax
    requires_shipping BOOLEAN DEFAULT true,
    taxable BOOLEAN DEFAULT true,

    -- Status
    is_active BOOLEAN DEFAULT true,

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Variant indexes
CREATE INDEX idx_product_variants_product ON product_variants(product_id);
CREATE INDEX idx_product_variants_sku ON product_variants(sku);
CREATE INDEX idx_product_variants_active ON product_variants(is_active) WHERE is_active = true;
CREATE INDEX idx_product_variants_options ON product_variants(option1_value, option2_value, option3_value);
```

### **3. Inventory Management** (Claude's Comprehensive Approach)
```sql
CREATE TABLE inventory (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

    -- Product reference (either product or variant, not both)
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    variant_id UUID REFERENCES product_variants(id) ON DELETE CASCADE,

    -- Location tracking
    location VARCHAR(100) DEFAULT 'default',
    warehouse_id UUID, -- Reference to warehouse system if needed

    -- Inventory quantities
    available_quantity INTEGER NOT NULL DEFAULT 0,
    reserved_quantity INTEGER DEFAULT 0,  -- Reserved for pending orders
    incoming_quantity INTEGER DEFAULT 0,  -- Expected stock

    -- Thresholds
    minimum_quantity INTEGER DEFAULT 0,
    maximum_quantity INTEGER,
    reorder_point INTEGER DEFAULT 0,
    reorder_quantity INTEGER DEFAULT 0,

    -- Tracking
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_counted_at TIMESTAMP WITH TIME ZONE,

    -- Constraints
    CHECK (product_id IS NOT NULL OR variant_id IS NOT NULL),
    CHECK (NOT (product_id IS NOT NULL AND variant_id IS NOT NULL))
);

-- Inventory indexes
CREATE INDEX idx_inventory_product ON inventory(product_id) WHERE product_id IS NOT NULL;
CREATE INDEX idx_inventory_variant ON inventory(variant_id) WHERE variant_id IS NOT NULL;
CREATE INDEX idx_inventory_location ON inventory(location);
CREATE INDEX idx_inventory_low_stock ON inventory(available_quantity, minimum_quantity)
    WHERE available_quantity <= minimum_quantity;
```

## 🛒 **Order Management** (Best from Claude + Gemini)

### **4. Orders**
```sql
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

    -- Customer reference (from main database)
    customer_id UUID NOT NULL, -- References main.customers (no FK constraint)
    customer_email VARCHAR(255) NOT NULL, -- Denormalized for convenience

    -- Order identification
    order_number VARCHAR(50) UNIQUE NOT NULL,

    -- Status tracking
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded')),
    financial_status VARCHAR(20) DEFAULT 'pending' CHECK (financial_status IN ('pending', 'authorized', 'paid', 'partially_paid', 'refunded', 'partially_refunded', 'voided')),
    fulfillment_status VARCHAR(20) DEFAULT 'unfulfilled' CHECK (fulfillment_status IN ('unfulfilled', 'partial', 'fulfilled')),

    -- Financial details
    subtotal DECIMAL(12,2) NOT NULL,
    tax_amount DECIMAL(12,2) DEFAULT 0.00,
    shipping_amount DECIMAL(12,2) DEFAULT 0.00,
    discount_amount DECIMAL(12,2) DEFAULT 0.00,
    total_amount DECIMAL(12,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',

    -- Customer information
    customer_phone VARCHAR(20),

    -- Addresses (JSONB for flexibility)
    shipping_address JSONB NOT NULL,
    billing_address JSONB NOT NULL,

    -- Additional information
    notes TEXT,
    tags TEXT[],

    -- Cross-tenant order reference
    cross_tenant_order_id UUID, -- References main.cross_tenant_orders

    -- Processing timestamps
    processed_at TIMESTAMP WITH TIME ZONE,
    shipped_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Order indexes
CREATE INDEX idx_orders_customer ON orders(customer_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_financial_status ON orders(financial_status);
CREATE INDEX idx_orders_fulfillment_status ON orders(fulfillment_status);
CREATE INDEX idx_orders_number ON orders(order_number);
CREATE INDEX idx_orders_created ON orders(created_at DESC);
CREATE INDEX idx_orders_cross_tenant ON orders(cross_tenant_order_id) WHERE cross_tenant_order_id IS NOT NULL;
```

### **5. Order Line Items**
```sql
CREATE TABLE order_line_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,

    -- Product references
    product_id UUID REFERENCES products(id) ON DELETE RESTRICT,
    variant_id UUID REFERENCES product_variants(id) ON DELETE RESTRICT,

    -- Product details at time of purchase (denormalized)
    title VARCHAR(255) NOT NULL,
    variant_title VARCHAR(255),
    sku VARCHAR(100),

    -- Quantity and pricing
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    price DECIMAL(12,2) NOT NULL, -- Unit price at time of purchase
    total_discount DECIMAL(12,2) DEFAULT 0.00,
    line_total DECIMAL(12,2) NOT NULL, -- (quantity * price) - total_discount

    -- Product attributes at time of purchase
    product_attributes JSONB DEFAULT '{}',

    -- Fulfillment
    fulfillment_status VARCHAR(20) DEFAULT 'unfulfilled' CHECK (fulfillment_status IN ('unfulfilled', 'fulfilled', 'returned')),

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Order line item indexes
CREATE INDEX idx_order_line_items_order ON order_line_items(order_id);
CREATE INDEX idx_order_line_items_product ON order_line_items(product_id);
CREATE INDEX idx_order_line_items_variant ON order_line_items(variant_id);
```

## 🛍️ **Shopping Cart & Customer Features** (Claude's Enhancement)

### **6. Shopping Carts**
```sql
CREATE TABLE carts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

    -- Customer reference (nullable for guest carts)
    customer_id UUID, -- References main.customers (no FK constraint)
    session_id VARCHAR(255), -- For guest carts

    -- Cart status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'abandoned', 'completed', 'expired')),

    -- Financial summary
    subtotal DECIMAL(12,2) DEFAULT 0.00,
    tax_amount DECIMAL(12,2) DEFAULT 0.00,
    shipping_amount DECIMAL(12,2) DEFAULT 0.00,
    discount_amount DECIMAL(12,2) DEFAULT 0.00,
    total_amount DECIMAL(12,2) DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'USD',

    -- Cart metadata
    notes TEXT,

    -- Expiration
    expires_at TIMESTAMP WITH TIME ZONE,

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Cart indexes
CREATE INDEX idx_carts_customer ON carts(customer_id) WHERE customer_id IS NOT NULL;
CREATE INDEX idx_carts_session ON carts(session_id) WHERE session_id IS NOT NULL;
CREATE INDEX idx_carts_status ON carts(status);
CREATE INDEX idx_carts_expires ON carts(expires_at) WHERE expires_at IS NOT NULL;
```

### **7. Cart Items**
```sql
CREATE TABLE cart_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    cart_id UUID NOT NULL REFERENCES carts(id) ON DELETE CASCADE,

    -- Product references
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    variant_id UUID REFERENCES product_variants(id) ON DELETE CASCADE,

    -- Quantity and pricing
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    price DECIMAL(12,2) NOT NULL, -- Current price when added

    -- Custom attributes (for personalization, gift messages, etc.)
    custom_attributes JSONB DEFAULT '{}',

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure unique product/variant per cart
    UNIQUE(cart_id, product_id, variant_id)
);

-- Cart item indexes
CREATE INDEX idx_cart_items_cart ON cart_items(cart_id);
CREATE INDEX idx_cart_items_product ON cart_items(product_id);
CREATE INDEX idx_cart_items_variant ON cart_items(variant_id) WHERE variant_id IS NOT NULL;
```

## ⭐ **Reviews & Marketing** (Claude's Social Commerce)

### **8. Product Reviews**
```sql
CREATE TABLE product_reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,

    -- Customer reference (from main database)
    customer_id UUID NOT NULL, -- References main.customers (no FK constraint)
    order_id UUID REFERENCES orders(id), -- Optional: link to purchase

    -- Review content
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title VARCHAR(255),
    content TEXT,

    -- Review metadata
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'spam')),
    helpful_count INTEGER DEFAULT 0,
    reported_count INTEGER DEFAULT 0,

    -- Verification
    is_verified_purchase BOOLEAN DEFAULT false,

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Prevent duplicate reviews per customer per product
    UNIQUE(product_id, customer_id)
);

-- Review indexes
CREATE INDEX idx_reviews_product ON product_reviews(product_id);
CREATE INDEX idx_reviews_customer ON product_reviews(customer_id);
CREATE INDEX idx_reviews_status ON product_reviews(status);
CREATE INDEX idx_reviews_rating ON product_reviews(rating);
CREATE INDEX idx_reviews_verified ON product_reviews(is_verified_purchase) WHERE is_verified_purchase = true;
```

### **9. Coupons & Discounts**
```sql
CREATE TABLE coupons (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

    -- Coupon identification
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,

    -- Discount configuration
    type VARCHAR(20) NOT NULL CHECK (type IN ('percentage', 'fixed_amount', 'free_shipping', 'buy_x_get_y')),
    value DECIMAL(12,2) NOT NULL,

    -- Conditions
    minimum_amount DECIMAL(12,2),
    maximum_discount DECIMAL(12,2),
    applicable_products JSONB, -- Array of product IDs
    applicable_categories JSONB, -- Array of category IDs

    -- Usage limits
    usage_limit INTEGER, -- Total usage limit
    usage_count INTEGER DEFAULT 0,
    customer_usage_limit INTEGER DEFAULT 1, -- Per customer limit

    -- Validity period
    starts_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,

    -- Status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'expired')),

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Coupon indexes
CREATE INDEX idx_coupons_code ON coupons(code);
CREATE INDEX idx_coupons_status ON coupons(status) WHERE status = 'active';
CREATE INDEX idx_coupons_expires ON coupons(expires_at);
CREATE INDEX idx_coupons_starts ON coupons(starts_at);
```

### **10. Coupon Usage Tracking**
```sql
CREATE TABLE coupon_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    coupon_id UUID NOT NULL REFERENCES coupons(id) ON DELETE CASCADE,

    -- Customer reference (from main database)
    customer_id UUID NOT NULL, -- References main.customers (no FK constraint)
    order_id UUID REFERENCES orders(id),

    -- Usage details
    discount_amount DECIMAL(12,2) NOT NULL,

    -- Timestamp
    used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Coupon usage indexes
CREATE INDEX idx_coupon_usage_coupon ON coupon_usage(coupon_id);
CREATE INDEX idx_coupon_usage_customer ON coupon_usage(customer_id);
CREATE INDEX idx_coupon_usage_order ON coupon_usage(order_id);
```

## ⚙️ **Tenant Settings & Configuration**

### **11. Tenant Settings**
```sql
CREATE TABLE tenant_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

    -- Setting identification
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type VARCHAR(20) DEFAULT 'string' CHECK (setting_type IN ('string', 'number', 'boolean', 'json')),

    -- Metadata
    description TEXT,
    is_public BOOLEAN DEFAULT false, -- Can be accessed by frontend

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Settings indexes
CREATE INDEX idx_tenant_settings_key ON tenant_settings(setting_key);
CREATE INDEX idx_tenant_settings_public ON tenant_settings(is_public) WHERE is_public = true;
```
```
