import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Category } from './entities/category.entity';
import { Subcategory } from './entities/subcategory.entity';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { CreateSubcategoryDto } from './dto/create-subcategory.dto';
import { UpdateSubcategoryDto } from './dto/update-subcategory.dto';

@Injectable()
export class CategoriesService {
  constructor(
    @InjectRepository(Category)
    private readonly categoryRepository: Repository<Category>,
    @InjectRepository(Subcategory)
    private readonly subcategoryRepository: Repository<Subcategory>,
  ) {}

  async createCategory(createCategoryDto: CreateCategoryDto): Promise<Category> {
    const existingCategory = await this.categoryRepository.findOne({
      where: { name: createCategoryDto.name },
    });

    if (existingCategory) {
      throw new ConflictException('Category with this name already exists');
    }

    const category = this.categoryRepository.create(createCategoryDto);
    return this.categoryRepository.save(category);
  }

  async findAllCategories(): Promise<Category[]> {
    return this.categoryRepository.find({
      relations: ['subcategories'],
      order: {
        name: 'ASC',
        subcategories: {
          name: 'ASC',
        },
      },
    });
  }

  async findOneCategory(id: string): Promise<Category> {
    const category = await this.categoryRepository.findOne({
      where: { id },
      relations: ['subcategories'],
    });

    if (!category) {
      throw new NotFoundException('Category not found');
    }

    return category;
  }

  async updateCategory(id: string, updateCategoryDto: UpdateCategoryDto): Promise<Category> {
    const category = await this.findOneCategory(id);

    if (updateCategoryDto.name) {
      const existingCategory = await this.categoryRepository.findOne({
        where: { name: updateCategoryDto.name },
      });

      if (existingCategory && existingCategory.id !== id) {
        throw new ConflictException('Category with this name already exists');
      }
    }

    Object.assign(category, updateCategoryDto);
    return this.categoryRepository.save(category);
  }

  async removeCategory(id: string): Promise<void> {
    const category = await this.findOneCategory(id);
    await this.categoryRepository.remove(category);
  }

  async createSubcategory(createSubcategoryDto: CreateSubcategoryDto): Promise<Subcategory> {
    const category = await this.findOneCategory(createSubcategoryDto.categoryId);

    const existingSubcategory = await this.subcategoryRepository.findOne({
      where: {
        name: createSubcategoryDto.name,
        categoryId: category.id,
      },
    });

    if (existingSubcategory) {
      throw new ConflictException('Subcategory with this name already exists in the category');
    }

    const subcategory = this.subcategoryRepository.create(createSubcategoryDto);
    return this.subcategoryRepository.save(subcategory);
  }

  async findAllSubcategories(): Promise<Subcategory[]> {
    return this.subcategoryRepository.find({
      relations: ['category'],
      order: { name: 'ASC' },
    });
  }

  async findOneSubcategory(id: string): Promise<Subcategory> {
    const subcategory = await this.subcategoryRepository.findOne({
      where: { id },
      relations: ['category'],
    });

    if (!subcategory) {
      throw new NotFoundException('Subcategory not found');
    }

    return subcategory;
  }

  async updateSubcategory(id: string, updateSubcategoryDto: UpdateSubcategoryDto): Promise<Subcategory> {
    const subcategory = await this.findOneSubcategory(id);

    if (updateSubcategoryDto.name) {
      const existingSubcategory = await this.subcategoryRepository.findOne({
        where: {
          name: updateSubcategoryDto.name,
          categoryId: updateSubcategoryDto.categoryId || subcategory.categoryId,
        },
      });

      if (existingSubcategory && existingSubcategory.id !== id) {
        throw new ConflictException('Subcategory with this name already exists in the category');
      }
    }

    if (updateSubcategoryDto.categoryId) {
      await this.findOneCategory(updateSubcategoryDto.categoryId);
    }

    Object.assign(subcategory, updateSubcategoryDto);
    return this.subcategoryRepository.save(subcategory);
  }

  async removeSubcategory(id: string): Promise<void> {
    const subcategory = await this.findOneSubcategory(id);
    await this.subcategoryRepository.remove(subcategory);
  }
}
