import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { OrderItem } from './order-item.entity';

export enum OrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  PROCESSING = 'processing',
  SHIPPED = 'shipped',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
}

export enum PaymentStatus {
  PENDING = 'pending',
  PAID = 'paid',
  FAILED = 'failed',
  REFUNDED = 'refunded',
}

@Entity('orders')
export class Order {
  @ApiProperty({
    description: 'Unique identifier for the order',
    example: '3cbd87ba-7a13-4abf-aafa-c72bb2b7eeb6',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'Human-readable order number',
    example: 'ORD-2025-001234',
  })
  @Column({ unique: true })
  orderNumber: string;

  @ApiProperty({
    description: 'Customer ID from main database',
    example: '3cbd87ba-7a13-4abf-aafa-c72bb2b7eeb6',
  })
  @Column('uuid')
  customerId: string;

  @ApiProperty({
    description: 'Vendor ID',
    example: '3cbd87ba-7a13-4abf-aafa-c72bb2b7eeb6',
  })
  @Column('uuid')
  vendorId: string;

  @ApiProperty({
    description: 'Cross-tenant order ID for multi-vendor orders',
    example: '3cbd87ba-7a13-4abf-aafa-c72bb2b7eeb6',
    required: false,
  })
  @Column('uuid', { nullable: true })
  crossTenantOrderId: string;

  @ApiProperty({
    description: 'Order status',
    enum: OrderStatus,
    example: OrderStatus.PENDING,
  })
  @Column({
    type: 'enum',
    enum: OrderStatus,
    default: OrderStatus.PENDING,
  })
  status: OrderStatus;

  @ApiProperty({
    description: 'Payment status',
    enum: PaymentStatus,
    example: PaymentStatus.PENDING,
  })
  @Column({
    type: 'enum',
    enum: PaymentStatus,
    default: PaymentStatus.PENDING,
  })
  paymentStatus: PaymentStatus;

  @ApiProperty({
    description: 'Subtotal amount (before tax and shipping)',
    example: 99.99,
  })
  @Column('decimal', { precision: 12, scale: 2 })
  subtotal: number;

  @ApiProperty({
    description: 'Tax amount',
    example: 8.00,
  })
  @Column('decimal', { precision: 12, scale: 2, default: 0 })
  taxAmount: number;

  @ApiProperty({
    description: 'Shipping cost',
    example: 5.99,
  })
  @Column('decimal', { precision: 12, scale: 2, default: 0 })
  shippingCost: number;

  @ApiProperty({
    description: 'Total amount (subtotal + tax + shipping)',
    example: 113.98,
  })
  @Column('decimal', { precision: 12, scale: 2 })
  totalAmount: number;

  @ApiProperty({
    description: 'Currency code',
    example: 'USD',
  })
  @Column({ length: 3, default: 'USD' })
  currency: string;

  @ApiProperty({
    description: 'Shipping address',
    example: {
      name: 'John Doe',
      street: '123 Main St',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      country: 'USA',
    },
  })
  @Column('jsonb')
  shippingAddress: {
    name: string;
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    phone?: string;
  };

  @ApiProperty({
    description: 'Billing address',
    example: {
      name: 'John Doe',
      street: '123 Main St',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      country: 'USA',
    },
  })
  @Column('jsonb')
  billingAddress: {
    name: string;
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    phone?: string;
  };

  @ApiProperty({
    description: 'Payment method information',
    example: {
      type: 'credit_card',
      last4: '1234',
      brand: 'visa',
    },
    required: false,
  })
  @Column('jsonb', { nullable: true })
  paymentMethod: {
    type: string;
    last4?: string;
    brand?: string;
    transactionId?: string;
  };

  @ApiProperty({
    description: 'Order notes from customer',
    example: 'Please deliver after 5 PM',
    required: false,
  })
  @Column('text', { nullable: true })
  notes: string;

  @ApiProperty({
    description: 'Tracking number for shipment',
    example: 'TRK123456789',
    required: false,
  })
  @Column({ nullable: true })
  trackingNumber: string;

  @ApiProperty({
    description: 'Estimated delivery date',
    example: '2025-06-01T00:00:00.000Z',
    required: false,
  })
  @Column({ type: 'timestamp', nullable: true })
  estimatedDeliveryDate: Date;

  @ApiProperty({
    description: 'When this order was created',
    example: '2025-05-26T09:30:00.000Z',
  })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({
    description: 'When this order was last updated',
    example: '2025-05-26T09:30:00.000Z',
  })
  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @OneToMany(() => OrderItem, (orderItem) => orderItem.order, { cascade: true })
  items: OrderItem[];
}
