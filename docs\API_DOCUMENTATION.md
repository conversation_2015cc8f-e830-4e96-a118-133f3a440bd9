# Multi-Tenant E-commerce API Documentation

## Overview

The Multi-Tenant E-commerce API is a robust backend solution built with NestJS, TypeScript, TypeORM, and PostgreSQL. It enables multiple vendors to operate their own e-commerce stores while sharing a common infrastructure. Each vendor gets their own dedicated database while maintaining a shared user base.

## Architecture

### Multi-Tenancy Approach

This application implements a database-per-tenant architecture:

1. **Main Database**
   - User accounts (super-admin, vendor, customer)
   - Tenant information (stored in the vendor entity)
   - Categories and subcategories (managed by super admin)

2. **Tenant-Specific Databases**
   - Tenant-specific products
   - Tenant-specific orders

### Tenant Identification

Tenants are identified by subdomains:
- Example: `vendor1.example.com`, `vendor2.example.com`
- The subdomain is extracted from the request host header
- The tenant middleware automatically detects the current tenant

## User Roles

### Super Admin
- Manages the entire platform
- Creates and manages categories/subcategories
- Oversees all vendors
- Has access to all data across the platform

### Vendor
- Manages their own store
- Creates and manages their products
- Processes orders from their store
- Has access only to their own data
- Each vendor has their own subdomain and database

### Customer
- Browses products from all vendors
- Places orders
- Can use the same account across all vendor stores
- Registration source is tracked for analytics

## Authentication

The API uses JWT (JSON Web Token) for authentication:

- Access tokens with configurable expiration
- Refresh tokens for extended sessions
- Token invalidation on logout
- Security features:
  - Rate limiting to prevent brute force attacks
  - Login attempt tracking
  - IP and user agent tracking

## API Endpoints

## Authentication Endpoints

### Super Admin Routes
- `POST /auth/superadmin/login` - Super admin login
  - Request body: `{ "email": string, "password": string }`
  - Response: `{ "access_token": string, "refresh_token": string, "user": object }`

- `POST /auth/superadmin/register` - Register super admin (initial setup only)
  - Request body: `{ "email": string, "password": string, "passwordConfirmation": string }`
  - Response: Created user object

### Vendor Routes
- `POST /auth/vendor/login` - Vendor login
  - Request body: `{ "email": string, "password": string }`
  - Response: `{ "access_token": string, "refresh_token": string, "user": object }`

- `POST /auth/vendor/register` - Register new vendor
  - Request body: `{ "email": string, "password": string, "passwordConfirmation": string, "storeName": string }`
  - Response: Created vendor object

### Customer Routes
- `POST /auth/customer/login` - Customer login
  - Request body: `{ "email": string, "password": string }`
  - Response: `{ "access_token": string, "refresh_token": string, "user": object }`

- `POST /auth/customer/register` - Register new customer
  - Request body: `{ "email": string, "password": string, "passwordConfirmation": string }`
  - Response: Created customer object

### Common Auth Routes
- `POST /auth/refresh` - Refresh access token
  - Request body: `{ "refreshToken": string }`
  - Response: `{ "access_token": string, "refresh_token": string }`

- `POST /auth/logout` - Logout user (requires authentication)
  - Headers: `Authorization: Bearer <token>`
  - Response: `{ "message": "Successfully logged out" }`

- `GET /auth/profile` - Get current user profile (requires authentication)
  - Headers: `Authorization: Bearer <token>`
  - Response: User profile object

### Tenants

| Endpoint | Method | Description | Access |
|----------|--------|-------------|--------|
| `/tenants` | GET | Get all tenants | Super Admin |
| `/tenants/:id` | GET | Get tenant by ID | Super Admin |
| `/tenants` | POST | Create a new tenant | Vendor |

### Users

| Endpoint | Method | Description | Access |
|----------|--------|-------------|--------|
| `/users` | GET | Get all users | Super Admin |
| `/users/:id` | GET | Get user by ID | Super Admin, Owner |
| `/users/:id` | PATCH | Update user | Super Admin, Owner |
| `/users/:id` | DELETE | Delete user | Super Admin |

### Products (Planned)

| Endpoint | Method | Description | Access |
|----------|--------|-------------|--------|
| `/products` | GET | Get all products | Public |
| `/products/:id` | GET | Get product by ID | Public |
| `/products` | POST | Create a product | Vendor |
| `/products/:id` | PATCH | Update product | Vendor (owner) |
| `/products/:id` | DELETE | Delete product | Vendor (owner) |

### Categories (Planned)

| Endpoint | Method | Description | Access |
|----------|--------|-------------|--------|
| `/categories` | GET | Get all categories | Public |
| `/categories/:id` | GET | Get category by ID | Public |
| `/categories` | POST | Create a category | Super Admin |
| `/categories/:id` | PATCH | Update category | Super Admin |
| `/categories/:id` | DELETE | Delete category | Super Admin |

### Orders (Planned)

| Endpoint | Method | Description | Access |
|----------|--------|-------------|--------|
| `/orders` | GET | Get all orders | Vendor (own), Customer (own) |
| `/orders/:id` | GET | Get order by ID | Vendor (own), Customer (own) |
| `/orders` | POST | Create an order | Customer |
| `/orders/:id` | PATCH | Update order status | Vendor (own) |

## Request/Response Examples

### Register a Vendor

**Request:**
```json
POST /auth/register/vendor
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "storeName": "Awesome Store",
  "description": "The best store for awesome products",
  "contactPhone": "+1234567890",
  "contactEmail": "<EMAIL>",
  "address": "123 Awesome St, City, Country"
}
```

**Response:**
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "email": "<EMAIL>",
  "storeName": "Awesome Store",
  "description": "The best store for awesome products",
  "contactPhone": "+1234567890",
  "contactEmail": "<EMAIL>",
  "address": "123 Awesome St, City, Country",
  "createdAt": "2023-04-01T12:00:00.000Z",
  "updatedAt": "2023-04-01T12:00:00.000Z"
}
```

### Login

**Request:**
```json
POST /auth/login
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Response:**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "email": "<EMAIL>",
    "role": "vendor"
  }
}
```

## Security Considerations

1. **Rate Limiting**: The API implements rate limiting to prevent brute force attacks.
2. **JWT Security**: Access tokens have a short lifespan, while refresh tokens are used for extended sessions.
3. **Password Security**: Passwords are hashed using bcrypt before storage.
4. **CORS Protection**: Cross-Origin Resource Sharing is configured to allow only specific origins.
5. **Input Validation**: All input data is validated using class-validator.

## Security Features

- Role-specific login endpoints to prevent role confusion
- JWT-based authentication with refresh tokens
- Token blacklisting for logged-out sessions
- IP and user agent validation
- Rate limiting on login attempts
- Account locking after multiple failed attempts

## Development Setup

1. Clone the repository
2. Install dependencies: `npm install`
3. Configure environment variables (copy `.env.example` to `.env`)
4. Create the main database: `createdb tenant_main`
5. Start the application: `npm run start:dev`
6. Access the Swagger documentation: `http://localhost:3000/api/docs`

## Testing

- Unit tests: `npm run test`
- End-to-end tests: `npm run test:e2e`
- Test coverage: `npm run test:cov`

## Database Migrations

- Generate migration: `npm run migration:generate -- -n MigrationName`
- Run migrations: `npm run migration:run`
- Revert migration: `npm run migration:revert`
