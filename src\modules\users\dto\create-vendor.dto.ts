import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>Empty, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ength, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateVendorDto {
  @ApiProperty({
    description: 'Store name (must be unique)',
    example: 'Demo',
  })
  @IsString()
  @IsNotEmpty()
  storeName: string;

  @ApiProperty({
    description: 'Vendor email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'Vendor password (must contain at least 1 uppercase letter, 1 lowercase letter, and 1 number or special character)',
    example: 'Demo@123',
    minLength: 8,
  })
  @IsString()
  @MinLength(8)
  @Matches(/((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, {
    message: 'Password is too weak',
  })
  password: string;

  @ApiProperty({
    description: 'Password confirmation (must match password)',
    example: 'Demo@123',
    minLength: 8,
  })
  @IsString()
  @MinLength(8)
  passwordConfirmation: string;
}
