import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateProductsTable1748092685908 implements MigrationInterface {
    name = 'CreateProductsTable1748092685908'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create products table for tenant database
        await queryRunner.query(`
            CREATE TABLE "products" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "name" character varying NOT NULL,
                "description" text,
                "price" numeric(10,2) NOT NULL,
                "stock" integer NOT NULL DEFAULT '0',
                "images" text array DEFAULT '{}',
                "isActive" boolean NOT NULL DEFAULT true,
                "categoryId" uuid,
                "vendorId" uuid NOT NULL,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_products" PRIMARY KEY ("id")
            )
        `);

        // Note: Foreign key constraint to categories table in main database is not possible
        // across databases in PostgreSQL. Category validation should be handled at application level.
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop products table
        await queryRunner.query(`DROP TABLE "products"`);
    }
}
