import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { TenantsService } from '../tenants/tenants.service';
import { Order, OrderStatus, PaymentStatus } from './entities/order.entity';
import { OrderItem } from './entities/order-item.entity';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { Product } from '../products/entities/product.entity';

@Injectable()
export class OrdersService {
  constructor(private readonly tenantsService: TenantsService) {}

  async create(createOrderDto: CreateOrderDto, tenantId: string, vendorId: string): Promise<Order> {
    const tenantConnection = await this.tenantsService.getTenantConnection(tenantId);
    const orderRepository = tenantConnection.dataSource.getRepository(Order);
    const orderItemRepository = tenantConnection.dataSource.getRepository(OrderItem);
    const productRepository = tenantConnection.dataSource.getRepository(Product);

    // Generate order number
    const orderNumber = await this.generateOrderNumber(tenantId);

    // Validate products and calculate totals
    let subtotal = 0;
    const orderItems: Partial<OrderItem>[] = [];

    for (const itemDto of createOrderDto.items) {
      const product = await productRepository.findOne({
        where: { id: itemDto.productId, isActive: true },
      });

      if (!product) {
        throw new NotFoundException(`Product with ID "${itemDto.productId}" not found or inactive`);
      }

      if (product.stock < itemDto.quantity) {
        throw new BadRequestException(`Insufficient stock for product "${product.name}". Available: ${product.stock}, Requested: ${itemDto.quantity}`);
      }

      const itemTotal = product.price * itemDto.quantity;
      subtotal += itemTotal;

      orderItems.push({
        productId: product.id,
        productVariantId: itemDto.productVariantId,
        productName: product.name,
        productSku: `SKU-${product.id.slice(-8)}`,
        unitPrice: product.price,
        quantity: itemDto.quantity,
        totalPrice: itemTotal,
        productAttributes: {},
        productImage: Array.isArray(product.images) && product.images.length > 0 ? product.images[0] : undefined,
      });
    }

    // Calculate tax and shipping (simplified - in real app, use tax service)
    const taxAmount = subtotal * 0.08; // 8% tax
    const shippingCost = subtotal > 100 ? 0 : 9.99; // Free shipping over $100
    const totalAmount = subtotal + taxAmount + shippingCost;

    // Create order
    const order = orderRepository.create({
      orderNumber,
      customerId: createOrderDto.customerId,
      vendorId,
      crossTenantOrderId: createOrderDto.crossTenantOrderId,
      status: OrderStatus.PENDING,
      paymentStatus: PaymentStatus.PENDING,
      subtotal,
      taxAmount,
      shippingCost,
      totalAmount,
      shippingAddress: createOrderDto.shippingAddress,
      billingAddress: createOrderDto.billingAddress || createOrderDto.shippingAddress,
      notes: createOrderDto.notes,
    });

    const savedOrder = await orderRepository.save(order);

    // Create order items
    for (const itemData of orderItems) {
      const orderItem = orderItemRepository.create({
        ...itemData,
        orderId: savedOrder.id,
      });
      await orderItemRepository.save(orderItem);

      // Update product stock
      await productRepository.decrement(
        { id: itemData.productId },
        'stock',
        itemData.quantity!
      );
    }

    // Return order with items
    const orderWithItems = await orderRepository.findOne({
      where: { id: savedOrder.id },
      relations: ['items'],
    });

    if (!orderWithItems) {
      throw new Error('Failed to retrieve created order');
    }

    return orderWithItems;
  }

  async findAll(tenantId: string, customerId?: string): Promise<Order[]> {
    const tenantConnection = await this.tenantsService.getTenantConnection(tenantId);
    const repository = tenantConnection.dataSource.getRepository(Order);

    const whereCondition: any = {};
    if (customerId) {
      whereCondition.customerId = customerId;
    }

    return repository.find({
      where: whereCondition,
      relations: ['items'],
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string, tenantId: string): Promise<Order> {
    const tenantConnection = await this.tenantsService.getTenantConnection(tenantId);
    const repository = tenantConnection.dataSource.getRepository(Order);

    const order = await repository.findOne({
      where: { id },
      relations: ['items'],
    });

    if (!order) {
      throw new NotFoundException(`Order with ID "${id}" not found`);
    }

    return order;
  }

  async update(id: string, updateOrderDto: UpdateOrderDto, tenantId: string): Promise<Order> {
    const tenantConnection = await this.tenantsService.getTenantConnection(tenantId);
    const repository = tenantConnection.dataSource.getRepository(Order);

    const order = await repository.findOne({
      where: { id },
    });

    if (!order) {
      throw new NotFoundException(`Order with ID "${id}" not found`);
    }

    // Validate status transitions
    if (updateOrderDto.status && !this.isValidStatusTransition(order.status, updateOrderDto.status)) {
      throw new BadRequestException(`Invalid status transition from ${order.status} to ${updateOrderDto.status}`);
    }

    const updatedOrder = repository.merge(order, updateOrderDto);
    await repository.save(updatedOrder);

    const orderWithItems = await repository.findOne({
      where: { id },
      relations: ['items'],
    });

    if (!orderWithItems) {
      throw new NotFoundException(`Order with ID "${id}" not found after update`);
    }

    return orderWithItems;
  }

  async cancel(id: string, tenantId: string): Promise<Order> {
    const tenantConnection = await this.tenantsService.getTenantConnection(tenantId);
    const orderRepository = tenantConnection.dataSource.getRepository(Order);
    const productRepository = tenantConnection.dataSource.getRepository(Product);

    const order = await orderRepository.findOne({
      where: { id },
      relations: ['items'],
    });

    if (!order) {
      throw new NotFoundException(`Order with ID "${id}" not found`);
    }

    if (order.status === OrderStatus.SHIPPED || order.status === OrderStatus.DELIVERED) {
      throw new BadRequestException('Cannot cancel shipped or delivered orders');
    }

    // Restore product stock
    for (const item of order.items) {
      await productRepository.increment(
        { id: item.productId },
        'stock',
        item.quantity
      );
    }

    order.status = OrderStatus.CANCELLED;
    return orderRepository.save(order);
  }

  private async generateOrderNumber(tenantId: string): Promise<string> {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    // Get tenant info for prefix
    const vendor = await this.tenantsService.findBySubdomain(tenantId);
    const prefix = vendor?.subdomain?.toUpperCase().substring(0, 3) || 'ORD';

    const timestamp = Date.now().toString().slice(-6);
    return `${prefix}-${year}${month}${day}-${timestamp}`;
  }

  private isValidStatusTransition(currentStatus: OrderStatus, newStatus: OrderStatus): boolean {
    const validTransitions: Record<OrderStatus, OrderStatus[]> = {
      [OrderStatus.PENDING]: [OrderStatus.CONFIRMED, OrderStatus.CANCELLED],
      [OrderStatus.CONFIRMED]: [OrderStatus.PROCESSING, OrderStatus.CANCELLED],
      [OrderStatus.PROCESSING]: [OrderStatus.SHIPPED, OrderStatus.CANCELLED],
      [OrderStatus.SHIPPED]: [OrderStatus.DELIVERED],
      [OrderStatus.DELIVERED]: [OrderStatus.REFUNDED],
      [OrderStatus.CANCELLED]: [],
      [OrderStatus.REFUNDED]: [],
    };

    return validTransitions[currentStatus]?.includes(newStatus) || false;
  }
}
