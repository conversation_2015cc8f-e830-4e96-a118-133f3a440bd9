import { DataSource, DataSourceOptions } from 'typeorm';
import * as dotenv from 'dotenv';

dotenv.config();

/**
 * Main Database Configuration
 *
 * This data source handles:
 * - User management (users, customers, vendors)
 * - Global categories and subcategories
 * - System-wide configuration
 *
 * Usage:
 * - npm run migration:generate:main <MigrationName>
 * - npm run migration:run:main
 * - npm run migration:revert:main
 * - npm run migration:show:main
 */

export const dataSourceOptions: DataSourceOptions = {
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432', 10),
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  database: process.env.DB_MAIN_DATABASE || 'postgres',
  entities: [
    // Main database entities only
    __dirname + '/../modules/users/entities/*.entity{.ts,.js}',
    __dirname + '/../modules/categories/entities/*.entity{.ts,.js}',
    __dirname + '/../modules/catalog/entities/*.entity{.ts,.js}',
  ],
  migrations: [__dirname + '/migrations/main/*{.ts,.js}'],
  synchronize: false,
  dropSchema: false,
  logging: process.env.DB_LOGGING === 'true',
  migrationsTableName: 'main_migrations', // Unique table name for main DB
};

const dataSource = new DataSource(dataSourceOptions);
export default dataSource;
