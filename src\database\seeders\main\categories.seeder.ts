import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Category } from '../../../modules/categories/entities/category.entity';
import { BaseSeeder } from './base.seeder';

@Injectable()
export class CategoriesSeeder extends BaseSeeder {
  constructor(
    @InjectRepository(Category)
    private readonly categoryRepository: Repository<Category>
  ) {
    super();
  }

  async seed(): Promise<void> {
    try {
      const categories = [
        {
          name: 'Electronics',
          description: 'Electronic devices and accessories',
          image: 'https://example.com/electronics.jpg',
          isActive: true
        },
        {
          name: 'Fashion',
          description: 'Clothing, shoes, and accessories',
          image: 'https://example.com/fashion.jpg',
          isActive: true
        },
        {
          name: 'Home & Garden',
          description: 'Home decor, furniture, and garden supplies',
          image: 'https://example.com/home-garden.jpg',
          isActive: true
        },
        {
          name: 'Books',
          description: 'Books, magazines, and educational materials',
          image: 'https://example.com/books.jpg',
          isActive: true
        },
        {
          name: 'Sports & Outdoors',
          description: 'Sports equipment and outdoor gear',
          image: 'https://example.com/sports.jpg',
          isActive: true
        }
      ];

      for (const categoryData of categories) {
        const existingCategory = await this.categoryRepository.findOne({
          where: { name: categoryData.name }
        });

        if (!existingCategory) {
          const category = this.categoryRepository.create(categoryData);
          await this.categoryRepository.save(category);
        }
      }

      console.log('Categories seeding completed');
    } catch (error) {
      console.error('Error seeding categories:', error);
    }
  }
}
