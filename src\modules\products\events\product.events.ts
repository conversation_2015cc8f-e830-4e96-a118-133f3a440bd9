import { Product } from '../entities/product.entity';

export class ProductCreatedEvent {
  constructor(
    public readonly product: Product,
    public readonly tenantSubdomain: string,
    public readonly vendorId: string
  ) {}
}

export class ProductUpdatedEvent {
  constructor(
    public readonly product: Product,
    public readonly tenantSubdomain: string,
    public readonly vendorId: string
  ) {}
}

export class ProductDeletedEvent {
  constructor(
    public readonly productId: string,
    public readonly tenantSubdomain: string,
    public readonly vendorId: string
  ) {}
}

export class ProductStatusChangedEvent {
  constructor(
    public readonly productId: string,
    public readonly tenantSubdomain: string,
    public readonly vendorId: string,
    public readonly isActive: boolean
  ) {}
}
