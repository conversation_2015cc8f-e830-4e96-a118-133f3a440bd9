import { Injectable, NotFoundException, BadRequestException, Inject } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { EventBus } from '@nestjs/cqrs';
import { TenantsService } from '../tenants/tenants.service';
import { Product } from './entities/product.entity';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { TenantConnection } from '../../common/interfaces/tenant-connection.interface';
import {
  ProductCreatedEvent,
  ProductUpdatedEvent,
  ProductDeletedEvent
} from './events/product.events';

@Injectable()
export class ProductsService {
  constructor(
    private readonly tenantsService: TenantsService,
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,
    private eventBus: EventBus,
  ) {}

  async create(createProductDto: CreateProductDto, tenantId: string, vendorId: string): Promise<Product> {
    const tenantConnection = await this.tenantsService.getTenantConnection(tenantId);
    const repository = tenantConnection.dataSource.getRepository(Product);

    const product = repository.create({
      ...createProductDto,
      vendorId,
    });

    const savedProduct = await repository.save(product);

    // Get vendor info for event
    const vendor = await this.tenantsService.findBySubdomain(tenantId);
    if (vendor) {
      // Emit event for real-time catalog sync
      this.eventBus.publish(new ProductCreatedEvent(savedProduct, vendor.subdomain, vendorId));
    }

    // Invalidate cache
    await this.invalidateProductCache(vendorId, createProductDto.categoryId);

    return savedProduct;
  }

  async findAll(tenantId?: string): Promise<Product[]> {
    if (tenantId) {
      // Get products for specific tenant
      const tenantConnection = await this.tenantsService.getTenantConnection(tenantId);
      const repository = tenantConnection.dataSource.getRepository(Product);
      return repository.find({
        where: { isActive: true },
        order: { createdAt: 'DESC' },
        relations: ['category'],
      });
    } else {
      // Get products from all tenants (main catalog)
      const allTenants = await this.tenantsService.findAll();
      const allProducts: Product[] = [];

      for (const vendor of allTenants) {
        if (!vendor.isActive) continue;

        try {
          const tenantConnection = await this.tenantsService.getTenantConnection(vendor.subdomain);
          const repository = tenantConnection.dataSource.getRepository(Product);
          const products = await repository.find({
            where: { isActive: true },
            order: { createdAt: 'DESC' },
            relations: ['category'],
          });
          allProducts.push(...products);
        } catch (error) {
          console.error(`Error fetching products for tenant ${vendor.subdomain}:`, error);
          // Continue with next tenant
        }
      }

      return allProducts;
    }
  }

  async findOne(id: string, tenantId: string): Promise<Product> {
    const tenantConnection = await this.tenantsService.getTenantConnection(tenantId);
    const repository = tenantConnection.dataSource.getRepository(Product);

    const product = await repository.findOne({
      where: { id },
      relations: ['category'],
    });

    if (!product) {
      throw new NotFoundException(`Product with ID "${id}" not found`);
    }

    return product;
  }

  async update(id: string, updateProductDto: UpdateProductDto, tenantId: string, vendorId: string): Promise<Product> {
    const tenantConnection = await this.tenantsService.getTenantConnection(tenantId);
    const repository = tenantConnection.dataSource.getRepository(Product);

    const product = await repository.findOne({
      where: { id },
    });

    if (!product) {
      throw new NotFoundException(`Product with ID "${id}" not found`);
    }

    if (product.vendorId !== vendorId) {
      throw new BadRequestException('You can only update your own products');
    }

    const updatedProduct = repository.merge(product, updateProductDto);
    const savedProduct = await repository.save(updatedProduct);

    // Get vendor info for event
    const vendor = await this.tenantsService.findBySubdomain(tenantId);
    if (vendor) {
      // Emit event for real-time catalog sync
      this.eventBus.publish(new ProductUpdatedEvent(savedProduct, vendor.subdomain, vendorId));
    }

    // Invalidate cache
    await this.invalidateProductCache(vendorId, savedProduct.categoryId);
    await this.cacheManager.del(`product:${tenantId}:${id}`);

    return savedProduct;
  }

  async remove(id: string, tenantId: string, vendorId: string): Promise<void> {
    const tenantConnection = await this.tenantsService.getTenantConnection(tenantId);
    const repository = tenantConnection.dataSource.getRepository(Product);

    const product = await repository.findOne({
      where: { id },
    });

    if (!product) {
      throw new NotFoundException(`Product with ID "${id}" not found`);
    }

    if (product.vendorId !== vendorId) {
      throw new BadRequestException('You can only delete your own products');
    }

    await repository.remove(product);

    // Get vendor info for event
    const vendor = await this.tenantsService.findBySubdomain(tenantId);
    if (vendor) {
      // Emit event for real-time catalog sync
      this.eventBus.publish(new ProductDeletedEvent(id, vendor.subdomain, vendorId));
    }

    // Invalidate cache
    await this.invalidateProductCache(vendorId, product.categoryId);
    await this.cacheManager.del(`product:${tenantId}:${id}`);
  }

  /**
   * Invalidate cache for product-related entries
   */
  private async invalidateProductCache(vendorId?: string, categoryId?: string): Promise<void> {
    const patterns = ['products:catalog:*', 'search:*'];

    if (vendorId) {
      patterns.push(`products:vendor:${vendorId}:*`);
      patterns.push(`vendor:stats:${vendorId}`);
    }

    if (categoryId) {
      patterns.push(`products:category:${categoryId}:*`);
    }

    for (const pattern of patterns) {
      await this.cacheManager.del(pattern);
    }
  }
}
