import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Response, NextFunction } from 'express';
import { TenantRequest } from '../interfaces/tenant-request.interface';

@Injectable()
export class TenantMiddleware implements NestMiddleware {
  private readonly logger = new Logger(TenantMiddleware.name);

  use(req: TenantRequest, res: Response, next: NextFunction) {
    // Extract tenant from subdomain
    const host = req.headers.host;
    this.logger.debug(`Request host: ${host}`);

    // Handle both regular domains and localhost testing
    if (host?.includes('.')) {
      const parts = host.split('.');
      this.logger.debug(`Host parts: ${JSON.stringify(parts)}`);

      // For localhost testing (e.g., mystore.localhost:4000)
      if (parts.length >= 2 && parts[1].startsWith('localhost')) {
        const subdomain = parts[0];
        if (subdomain && subdomain !== 'www' && subdomain !== 'api') {
          req.tenantId = subdomain;
          this.logger.debug(`Tenant identified from localhost subdomain: ${subdomain}`);
        }
      }
      // For regular domains (e.g., mystore.example.com)
      else if (parts.length >= 3) {
        const subdomain = parts[0];
        if (subdomain && subdomain !== 'www' && subdomain !== 'api') {
          req.tenantId = subdomain;
          this.logger.debug(`Tenant identified from domain subdomain: ${subdomain}`);
        }
      }
    }

    // Special case for testing - allow setting tenant via query param in development
    if (process.env.NODE_ENV === 'development' && !req.tenantId && req.query.tenant) {
      req.tenantId = req.query.tenant as string;
      this.logger.debug(`Tenant set from query parameter: ${req.tenantId}`);
    }

    if (req.tenantId) {
      this.logger.log(`Request for tenant: ${req.tenantId}`);
    } else {
      this.logger.debug('No tenant identified for this request');
    }

    next();
  }
}
