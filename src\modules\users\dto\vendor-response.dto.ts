import { ApiProperty } from '@nestjs/swagger';

export class VendorResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the vendor',
    example: '3cbd87ba-7a13-4abf-aafa-c72bb2b7eeb6',
  })
  id: string;

  @ApiProperty({
    description: 'Store name',
    example: 'Demo',
  })
  storeName: string;

  @ApiProperty({
    description: 'Store description',
    example: 'A fantastic store with amazing products',
    required: false,
  })
  description: string | null;

  @ApiProperty({
    description: 'Store logo URL',
    example: 'https://example.com/logo.png',
    required: false,
  })
  logo: string | null;

  @ApiProperty({
    description: 'Store contact phone',
    example: '+1234567890',
    required: false,
  })
  contactPhone: string | null;

  @ApiProperty({
    description: 'Store contact email',
    example: '<EMAIL>',
    required: false,
  })
  contactEmail: string | null;

  @ApiProperty({
    description: 'Store address',
    example: '123 Main St, City, Country',
    required: false,
  })
  address: string | null;

  @ApiProperty({
    description: 'Store subdomain',
    example: 'demo',
  })
  subdomain: string;

  @ApiProperty({
    description: 'Whether the store is active',
    example: true,
  })
  isActive: boolean;

  @ApiProperty({
    description: 'Store creation date',
    example: '2025-05-22T15:49:59.634Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Store last update date',
    example: '2025-05-22T15:49:59.634Z',
  })
  updatedAt: Date;
}
