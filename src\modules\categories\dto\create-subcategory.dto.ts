import { IsString, IsOptional, IsBoolean, IsUUID } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateSubcategoryDto {
  @ApiProperty({
    description: 'The name of the subcategory',
    example: 'Lapt<PERSON>',
  })
  @IsString()
  name: string;

  @ApiPropertyOptional({
    description: 'The description of the subcategory',
    example: 'Portable computers and laptops',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'The image URL of the subcategory',
    example: 'https://example.com/laptops-subcategory.jpg',
  })
  @IsString()
  @IsOptional()
  image?: string;

  @ApiPropertyOptional({
    description: 'Whether the subcategory is active',
    example: true,
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiProperty({
    description: 'The UUID of the parent category',
    example: '3cbd87ba-7a13-4abf-aafa-c72bb2b7eeb6',
  })
  @IsUUID()
  categoryId: string;
}
