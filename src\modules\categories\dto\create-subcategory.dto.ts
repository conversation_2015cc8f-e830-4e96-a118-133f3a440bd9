import { IsString, IsOptional, IsBoolean, IsUUID } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateSubcategoryDto {
  @ApiProperty({ description: 'The name of the subcategory' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: 'The description of the subcategory' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({ description: 'The image URL of the subcategory' })
  @IsString()
  @IsOptional()
  image?: string;

  @ApiPropertyOptional({ description: 'Whether the subcategory is active' })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiProperty({ description: 'The UUID of the parent category' })
  @IsUUID()
  categoryId: string;
}
