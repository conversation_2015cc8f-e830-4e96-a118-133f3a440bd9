import { Injectable, ConflictException, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { User } from './entities/user.entity';
import { Vendor } from './entities/vendor.entity';
import { Customer } from './entities/customer.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { CreateVendorDto } from './dto/create-vendor.dto';
import { CreateCustomerDto } from './dto/create-customer.dto';
import { UserRole } from '../../common/interfaces/user-roles.enum';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(Vendor)
    private vendorsRepository: Repository<Vendor>,
    @InjectRepository(Customer)
    private customersRepository: Repository<Customer>,
  ) {}

  async createSuperAdmin(createUserDto: CreateUserDto): Promise<User> {
    // Check if passwords match
    if (createUserDto.password !== createUserDto.passwordConfirmation) {
      throw new BadRequestException('Passwords do not match');
    }

    // Check if user already exists
    const existingUser = await this.usersRepository.findOne({
      where: { email: createUserDto.email },
    });

    if (existingUser) {
      throw new ConflictException('Email already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(createUserDto.password, 10);

    // Create user
    const user = this.usersRepository.create({
      email: createUserDto.email,
      password: hashedPassword,
      role: UserRole.SUPER_ADMIN,
    });

    return this.usersRepository.save(user);
  }

  async createVendor(createVendorDto: CreateVendorDto): Promise<Vendor> {
    // Check if passwords match
    if (createVendorDto.password !== createVendorDto.passwordConfirmation) {
      throw new BadRequestException('Passwords do not match');
    }

    // Check if user already exists
    const existingUser = await this.usersRepository.findOne({
      where: { email: createVendorDto.email },
    });

    if (existingUser) {
      throw new ConflictException('Email already exists');
    }

    // Check if store name already exists
    const existingVendor = await this.vendorsRepository.findOne({
      where: { storeName: createVendorDto.storeName },
    });

    if (existingVendor) {
      throw new ConflictException('Store name already exists');
    }

    // Create subdomain from store name (lowercase, replace spaces with hyphens)
    const subdomain = createVendorDto.storeName.toLowerCase().replace(/\s+/g, '-');

    // Check if subdomain already exists
    const existingSubdomain = await this.vendorsRepository.findOne({
      where: { subdomain },
    });

    if (existingSubdomain) {
      throw new ConflictException(`Subdomain '${subdomain}' is already taken`);
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(createVendorDto.password, 10);

    // Create user
    const user = this.usersRepository.create({
      email: createVendorDto.email,
      password: hashedPassword,
      role: UserRole.VENDOR,
    });

    const savedUser = await this.usersRepository.save(user);

    // Create database name based on store name (replace hyphens with underscores for PostgreSQL compatibility)
    const databaseName = `tenant_${subdomain.replace(/-/g, '_')}`;

    // Create vendor with tenant information
    const vendor = this.vendorsRepository.create({
      storeName: createVendorDto.storeName,
      userId: savedUser.id,
      user: savedUser,
      subdomain: subdomain,
      databaseName: databaseName,
      isActive: true
    });

    return this.vendorsRepository.save(vendor);
  }

  async createCustomer(createCustomerDto: CreateCustomerDto, registrationSource?: string): Promise<Customer> {
    // Check if passwords match
    if (createCustomerDto.password !== createCustomerDto.passwordConfirmation) {
      throw new BadRequestException('Passwords do not match');
    }

    // Check if user already exists
    const existingUser = await this.usersRepository.findOne({
      where: { email: createCustomerDto.email },
    });

    if (existingUser) {
      throw new ConflictException('Email already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(createCustomerDto.password, 10);

    // Create user
    const user = this.usersRepository.create({
      email: createCustomerDto.email,
      password: hashedPassword,
      role: UserRole.CUSTOMER,
    });

    const savedUser = await this.usersRepository.save(user);

    // Create customer
    const customer = this.customersRepository.create({
      userId: savedUser.id,
      user: savedUser,
      registrationSource: registrationSource,
    });

    return this.customersRepository.save(customer);
  }

  async findOneByEmail(email: string): Promise<User> {
    const user = await this.usersRepository.findOne({
      where: { email },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async findOneById(id: string): Promise<User> {
    const user = await this.usersRepository.findOne({
      where: { id },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async findVendorByUserId(userId: string): Promise<Vendor> {
    const vendor = await this.vendorsRepository.findOne({
      where: { userId },
      relations: ['user'],
    });

    if (!vendor) {
      throw new NotFoundException('Vendor not found');
    }

    return vendor;
  }

  async findVendorByStoreName(storeName: string): Promise<Vendor> {
    const vendor = await this.vendorsRepository.findOne({
      where: { storeName },
      relations: ['user'],
    });

    if (!vendor) {
      throw new NotFoundException('Vendor not found');
    }

    return vendor;
  }

  async findCustomerByUserId(userId: string): Promise<Customer> {
    const customer = await this.customersRepository.findOne({
      where: { userId },
      relations: ['user'],
    });

    if (!customer) {
      throw new NotFoundException('Customer not found');
    }

    return customer;
  }

  // New methods for tenant functionality

  async findAllVendors(): Promise<Vendor[]> {
    return this.vendorsRepository.find({
      relations: ['user'],
    });
  }

  async findVendorById(id: string): Promise<Vendor> {
    const vendor = await this.vendorsRepository.findOne({
      where: { id },
      relations: ['user'],
    });

    if (!vendor) {
      throw new NotFoundException(`Vendor with ID ${id} not found`);
    }

    return vendor;
  }

  async findVendorBySubdomain(subdomain: string): Promise<Vendor> {
    const vendor = await this.vendorsRepository.findOne({
      where: { subdomain },
      relations: ['user'],
    });

    if (!vendor) {
      throw new NotFoundException(`Vendor with subdomain ${subdomain} not found`);
    }

    return vendor;
  }

  async updateVendor(vendor: Vendor): Promise<Vendor> {
    return this.vendorsRepository.save(vendor);
  }

  async incrementLoginAttempts(userId: string): Promise<void> {
    await this.usersRepository.update(userId, {
      loginAttempts: () => 'loginAttempts + 1',
      lastLoginAttempt: new Date(),
    });
  }

  async resetLoginAttempts(userId: string): Promise<void> {
    await this.usersRepository.update(userId, {
      loginAttempts: 0,
      lastLoginAttempt: undefined,
    });
  }

  async updateLastLoginInfo(userId: string, ip: string, userAgent?: string): Promise<void> {
    await this.usersRepository.update(userId, {
      lastLoginIp: ip,
      lastLoginUserAgent: userAgent,
    });
  }

  async storeRefreshToken(
    userId: string,
    refreshToken: string,
    metadata: {
      ip: string;
      userAgent: string;
      issuedAt: Date;
    },
  ): Promise<void> {
    await this.usersRepository.update(userId, {
      refreshToken,
      lastRefreshTokenIp: metadata.ip,
      lastRefreshTokenUserAgent: metadata.userAgent,
      lastRefreshTokenIssuedAt: metadata.issuedAt,
    });
  }

  async invalidateRefreshToken(userId: string): Promise<void> {
    await this.usersRepository.update(userId, {
      refreshToken: undefined,
    });
  }
}
