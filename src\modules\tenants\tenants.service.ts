import { Injectable, NotFoundException, ConflictException, ForbiddenException, UnauthorizedException } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { UsersService } from '../users/users.service';
import { TenantConnection } from '../../common/interfaces/tenant-connection.interface';
import { Vendor } from '../users/entities/vendor.entity';
import { TenantSeeder } from '../../database/seeders/tenant/tenant.seeder';
import { ProductsSeeder } from '../../database/seeders/tenant/products.seeder';

@Injectable()
export class TenantsService {
  private tenantConnections: Map<string, TenantConnection> = new Map();

  constructor(
    private usersService: UsersService,
    private configService: ConfigService,
    private dataSource: DataSource,
  ) {}

  async findAll(): Promise<Vendor[]> {
    // Get all vendors with tenant information (subdomain, databaseName)
    return this.usersService.findAllVendors();
  }

  async findOne(id: string): Promise<Vendor> {
    // Get vendor by ID
    const vendor = await this.usersService.findVendorById(id);

    if (!vendor) {
      throw new NotFoundException(`Tenant with ID ${id} not found`);
    }

    return vendor;
  }

  async findBySubdomain(subdomain: string): Promise<Vendor> {
    // Get vendor by subdomain
    const vendor = await this.usersService.findVendorBySubdomain(subdomain);

    if (!vendor) {
      throw new NotFoundException(`Tenant with subdomain ${subdomain} not found`);
    }

    return vendor;
  }

  async createTenant(userId: string): Promise<Vendor> {
    // Get vendor information
    const vendor = await this.usersService.findVendorByUserId(userId);

    // Check if tenant information already exists for this vendor
    if (vendor.subdomain) {
      throw new ConflictException(`Tenant already exists for vendor ${vendor.storeName}`);
    }

    // Create subdomain from store name (lowercase, replace spaces with hyphens)
    const subdomain = vendor.storeName.toLowerCase().replace(/\s+/g, '-');

    // Check if subdomain is already taken
    try {
      const existingVendor = await this.usersService.findVendorBySubdomain(subdomain);
      if (existingVendor) {
        throw new ConflictException(`Subdomain ${subdomain} is already taken`);
      }
    } catch (error) {
      if (!(error instanceof NotFoundException)) {
        throw error;
      }
      // If NotFoundException, then subdomain is available
    }

    // Create database name (replace hyphens with underscores for PostgreSQL compatibility)
    const databaseName = `tenant_${subdomain.replace(/-/g, '_')}`;

    // Update vendor with tenant information
    vendor.subdomain = subdomain;
    vendor.databaseName = databaseName;
    vendor.isActive = true;

    // Save vendor
    const savedVendor = await this.usersService.updateVendor(vendor);

    // Create tenant database
    await this.createTenantDatabase(savedVendor);

    return savedVendor;
  }

  async createTenantDatabase(vendor: Vendor): Promise<void> {
    try {
      // Create a new database for the tenant (with proper quoting)
      await this.dataSource.query(`CREATE DATABASE "${vendor.databaseName}"`);

      // Connect to the new database
      const tenantConnection = await this.connectToTenantDatabase(vendor);

      // Run migrations for the tenant database
      await this.runTenantMigrations(tenantConnection);

      // Run seeders for the tenant database if needed
      await this.runTenantSeeders(tenantConnection, vendor);
      
    } catch (error) {
      console.error('Error creating tenant database:', error);
      throw error;
    }
  }

  private async connectToTenantDatabase(vendor: Vendor): Promise<TenantConnection> {
    // Check if connection already exists
    if (this.tenantConnections.has(vendor.subdomain)) {
      const connection = this.tenantConnections.get(vendor.subdomain);
      if (connection) {
        return connection;
      }
    }

    // Create a new connection to the tenant database
    const dataSource = new DataSource({
      type: 'postgres',
      host: this.configService.get<string>('DB_HOST') || 'localhost',
      port: parseInt(this.configService.get<string>('DB_PORT') || '5432', 10),
      username: this.configService.get<string>('DB_USERNAME') || 'postgres',
      password: this.configService.get<string>('DB_PASSWORD') || 'postgres',
      database: vendor.databaseName,
      entities: [__dirname + '/../**/*.entity{.ts,.js}'],
      synchronize: true, // Always true for development
    });

    // Initialize the connection
    await dataSource.initialize();

    // Store the connection
    const connection: TenantConnection = {
      name: vendor.subdomain,
      dataSource,
    };

    this.tenantConnections.set(vendor.subdomain, connection);

    return connection;
  }

  private async runTenantMigrations(connection: TenantConnection): Promise<void> {
    try {
      // Run tenant-specific migrations from the migrations/tenant directory
      await connection.dataSource.runMigrations();
      console.log('Tenant migrations completed successfully');
    } catch (error) {
      console.error('Error running tenant migrations:', error);
      throw error;
    }
  }

  private async runTenantSeeders(connection: TenantConnection, vendor: Vendor): Promise<void> {
    try {
      // Initialize tenant seeders
      const productsSeeder = new ProductsSeeder(connection.dataSource);
      
      // Set the vendorId for demo products
      productsSeeder.updateVendorId(vendor.id);
      
      // Initialize and run the main tenant seeder
      const tenantSeeder = new TenantSeeder(connection.dataSource, productsSeeder);
      await tenantSeeder.seed();
      
      console.log('Tenant seeders completed successfully');
    } catch (error) {
      console.error('Error running tenant seeders:', error);
      throw error;
    }
  }

  async getTenantConnection(subdomain: string): Promise<TenantConnection> {
    // Check if connection already exists
    if (this.tenantConnections.has(subdomain)) {
      const connection = this.tenantConnections.get(subdomain);
      if (connection) {
        return connection;
      }
    }

    // Find vendor by subdomain
    const vendor = await this.findBySubdomain(subdomain);

    // Connect to tenant database
    return this.connectToTenantDatabase(vendor);
  }

  async validateVendorSubdomain(subdomain: string): Promise<Vendor> {
    try {
      // Get vendor by subdomain
      const vendor = await this.findBySubdomain(subdomain);

      // Check if vendor is active
      if (!vendor.isActive) {
        throw new ForbiddenException('Vendor account is not active');
      }

      return vendor;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new UnauthorizedException('Invalid vendor portal');
      }
      throw error;
    }
  }
}
