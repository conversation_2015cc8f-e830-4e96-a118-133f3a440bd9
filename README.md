# Multi-Tenant E-commerce API

A multi-tenant e-commerce API built with NestJS, TypeScript, TypeORM, and PostgreSQL. This API supports multiple vendors/tenants with separate databases for each vendor and specific user types (super-admin, vendor, customer).

## Features

- Multi-tenancy with database-per-tenant approach
- Subdomain-based tenant access
- User management with different roles (super-admin, vendor, customer)
- Authentication with JWT
- Product management with categories and subcategories
- Order management
- Shared product catalog across all tenants

## Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)
- PostgreSQL (v12 or higher)

## Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd multi-tenant-ecommerce-api
```

2. Install dependencies:

```bash
npm install
```

3. Configure environment variables:

Copy the `.env.example` file to `.env` and update the values according to your environment:

```bash
cp .env.example .env
```

4. Create the main database:

```bash
createdb tenant_main
```

5. Start the application:

```bash
npm run start:dev
```

## API Endpoints

### Authentication

- `POST /auth/register/super-admin` - Register a super admin
- `POST /auth/register/vendor` - Register a vendor
- `POST /auth/register/customer` - Register a customer
- `POST /auth/login` - Login for all user types
- `GET /auth/profile` - Get current user profile

### Tenants

- `GET /tenants` - Get all tenants (super admin only)
- `GET /tenants/:id` - Get tenant by ID (super admin only)
- `POST /tenants` - Create a new tenant (vendor only)

## Multi-Tenancy Approach

This application uses a database-per-tenant approach for multi-tenancy:

1. Main database contains:
   - User accounts (super-admin, vendor, customer)
   - Tenant information
   - Categories and subcategories (managed by super admin)

2. Tenant-specific databases contain:
   - Tenant-specific products
   - Tenant-specific orders

## User Types

1. **Super Admin**:
   - Manages categories and subcategories
   - Manages vendors
   - Has access to all data, all vendors data are listed in main domain

2. **Vendor**:
   - Manages their own products
   - Processes orders from their store
   - Has access only to their own data

3. **Customer**:
   - Can browse products from all vendors
   - Can place orders
   - Can login to any vendor's store with the same credentials

## Development

### Running Migrations

```bash
npm run migration:generate -- -n <migration-name>
npm run migration:run
```

### Testing

```bash
npm run test
npm run test:e2e
```

## License

This project is licensed under the MIT License.
