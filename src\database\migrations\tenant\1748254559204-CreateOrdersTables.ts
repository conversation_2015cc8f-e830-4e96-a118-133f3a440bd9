import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateOrdersTables1748254559204 implements MigrationInterface {
    name = 'CreateOrdersTables1748254559204'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "order_items" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "orderId" uuid NOT NULL, "productId" uuid NOT NULL, "productVariantId" uuid, "productName" character varying NOT NULL, "productSku" character varying, "unitPrice" numeric(12,2) NOT NULL, "quantity" integer NOT NULL, "totalPrice" numeric(12,2) NOT NULL, "productAttributes" jsonb, "productImage" character varying, CONSTRAINT "PK_005269d8574e6fac0493715c308" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."orders_status_enum" AS ENUM('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded')`);
        await queryRunner.query(`CREATE TYPE "public"."orders_paymentstatus_enum" AS ENUM('pending', 'paid', 'failed', 'refunded')`);
        await queryRunner.query(`CREATE TABLE "orders" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "orderNumber" character varying NOT NULL, "customerId" uuid NOT NULL, "vendorId" uuid NOT NULL, "crossTenantOrderId" uuid, "status" "public"."orders_status_enum" NOT NULL DEFAULT 'pending', "paymentStatus" "public"."orders_paymentstatus_enum" NOT NULL DEFAULT 'pending', "subtotal" numeric(12,2) NOT NULL, "taxAmount" numeric(12,2) NOT NULL DEFAULT '0', "shippingCost" numeric(12,2) NOT NULL DEFAULT '0', "totalAmount" numeric(12,2) NOT NULL, "currency" character varying(3) NOT NULL DEFAULT 'USD', "shippingAddress" jsonb NOT NULL, "billingAddress" jsonb NOT NULL, "paymentMethod" jsonb, "notes" text, "trackingNumber" character varying, "estimatedDeliveryDate" TIMESTAMP, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_59b0c3b34ea0fa5562342f24143" UNIQUE ("orderNumber"), CONSTRAINT "PK_710e2d4957aa5878dfe94e4ac2f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "order_items" ADD CONSTRAINT "FK_f1d359a55923bb45b057fbdab0d" FOREIGN KEY ("orderId") REFERENCES "orders"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "order_items" DROP CONSTRAINT "FK_f1d359a55923bb45b057fbdab0d"`);
        await queryRunner.query(`DROP TABLE "orders"`);
        await queryRunner.query(`DROP TYPE "public"."orders_paymentstatus_enum"`);
        await queryRunner.query(`DROP TYPE "public"."orders_status_enum"`);
        await queryRunner.query(`DROP TABLE "order_items"`);
    }

}
