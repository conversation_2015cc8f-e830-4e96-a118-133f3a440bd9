import { Is<PERSON>mail, <PERSON><PERSON>num, <PERSON><PERSON>otE<PERSON>y, <PERSON><PERSON><PERSON><PERSON>, IsString, <PERSON><PERSON><PERSON><PERSON>, Matches } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { UserRole } from '../../../common/interfaces/user-roles.enum';

export class CreateUserDto {
  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'User password (must contain at least 1 uppercase letter, 1 lowercase letter, and 1 number or special character)',
    example: 'Password123!',
    minLength: 8,
  })
  @IsString()
  @MinLength(8)
  @Matches(/((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, {
    message: 'Password is too weak',
  })
  password: string;

  @ApiProperty({
    description: 'Password confirmation (must match password)',
    example: 'Password123!',
    minLength: 8,
  })
  @IsString()
  @MinLength(8)
  passwordConfirmation: string;

  @ApiPropertyOptional({
    description: 'User role (optional, defaults to customer)',
    enum: UserRole,
    example: UserRole.CUSTOMER,
  })
  @IsEnum(UserRole)
  @IsOptional()
  role?: UserRole;
}
