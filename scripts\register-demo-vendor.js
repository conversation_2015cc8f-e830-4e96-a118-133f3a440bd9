/**
 * <PERSON>ript to register demo vendors
 * 
 * Usage: node scripts/register-demo-vendors.js
 */

require('dotenv').config();
const { Client } = require('pg');
const bcrypt = require('bcrypt');

async function registerDemoVendors() {
  // DB config from env or fallback
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT ? parseInt(process.env.DB_PORT, 10) : 5432,
    user: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'postgres',
    database: process.env.DB_MAIN_DATABASE || 'postgres',
  };

  const demoVendors = [
    {
      email: '<EMAIL>',
      password: 'Demo@123',
      storeName: 'Demo One',
    },
    {
      email: '<EMAIL>',
      password: 'Demo@123',
      storeName: 'Demo Two',
    },
  ];

  const client = new Client(dbConfig);

  try {
    await client.connect();
    console.log('Connected to main database.');

    for (const vendor of demoVendors) {
      const { email, password, storeName } = vendor;
      const subdomain = storeName.toLowerCase().replace(/\s+/g, '-');
      const databaseName = `tenant_${subdomain.replace(/-/g, '_')}`;

      // Check if user already exists
      const userRes = await client.query('SELECT * FROM users WHERE email = $1', [email]);
      let userId;
      if (userRes.rows.length > 0) {
        userId = userRes.rows[0].id;
        console.log(`User ${email} already exists. Skipping user creation.`);
      } else {
        const hashedPassword = await bcrypt.hash(password, 10);
        const insertUserRes = await client.query(
          `INSERT INTO users (email, password, role, "isActive", "createdAt", "updatedAt") VALUES ($1, $2, $3, $4, NOW(), NOW()) RETURNING id`,
          [email, hashedPassword, 'vendor', true]
        );
        userId = insertUserRes.rows[0].id;
        console.log(`User ${email} created.`);
      }

      // Check if vendor already exists
      const vendorRes = await client.query('SELECT * FROM vendors WHERE "storeName" = $1', [storeName]);
      let vendorId;
      if (vendorRes.rows.length > 0) {
        vendorId = vendorRes.rows[0].id;
        console.log(`Vendor '${storeName}' already exists. Skipping vendor creation.`);
      } else {
        const insertVendorRes = await client.query(
          `INSERT INTO vendors ("storeName", "userId", subdomain, "databaseName", "isActive", "createdAt", "updatedAt") VALUES ($1, $2, $3, $4, $5, NOW(), NOW()) RETURNING id`,
          [storeName, userId, subdomain, databaseName, true]
        );
        vendorId = insertVendorRes.rows[0].id;
        console.log(`Vendor '${storeName}' created.`);
      }

      // Create tenant database if it doesn't exist
      const dbExistsRes = await client.query('SELECT 1 FROM pg_database WHERE datname = $1', [databaseName]);
      if (dbExistsRes.rows.length === 0) {
        await client.query(`CREATE DATABASE "${databaseName}"`);
        console.log(`Tenant database '${databaseName}' created.`);
      } else {
        console.log(`Tenant database '${databaseName}' already exists.`);
      }

      console.log(`\nVendor '${storeName}' setup complete!`);
      console.log(`Access: http://${subdomain}.localhost:4000`);
      console.log(`Email: ${email}`);
      console.log(`Password: ${password}\n`);
    }
  } catch (error) {
    console.error('Error:', error.message);
    console.log('\nEnsure the main database is accessible and the tables exist.');
  } finally {
    await client.end();
  }
}

registerDemoVendors();
