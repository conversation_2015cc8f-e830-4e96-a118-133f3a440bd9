import { Injectable } from '@nestjs/common';
import { DataSource, DeepPartial } from 'typeorm';
import { Product } from '../../../modules/products/entities/product.entity';
import { BaseTenantSeeder } from './base.seeder';

@Injectable()
export class ProductsSeeder extends BaseTenantSeeder {
  private readonly products: DeepPartial<Product>[] = [
    {
      name: 'Demo Product 1',
      description: 'This is a demo product for the tenant',
      price: 99.99,
      stock: 100,
      isActive: true,
      vendorId: '', // This will be set by the tenant service
      images: ['https://example.com/demo-product-1.jpg']
    },
    {
      name: 'Demo Product 2',
      description: 'Another demo product for the tenant',
      price: 149.99,
      stock: 50,
      isActive: true,
      vendorId: '', // This will be set by the tenant service
      images: ['https://example.com/demo-product-2.jpg']
    }
  ];

  constructor(dataSource: DataSource) {
    super(dataSource);
  }

  async seed(): Promise<void> {
    try {
      const repository = this.dataSource.getRepository(Product);

      for (const productData of this.products) {
        const existingProduct = await repository.findOne({
          where: { name: productData.name }
        });

        if (!existingProduct) {
          const product = repository.create(productData);
          await repository.save(product);
        }
      }

      console.log('Products seeding completed for tenant');
    } catch (error) {
      console.error('Error seeding tenant products:', error);
      throw error;
    }
  }

  updateVendorId(vendorId: string): void {
    this.products.forEach(product => {
      product.vendorId = vendorId;
    });
  }
}
