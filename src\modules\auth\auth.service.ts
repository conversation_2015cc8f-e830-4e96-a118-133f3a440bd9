import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { UsersService } from '../users/users.service';
import { TenantsService } from '../tenants/tenants.service';
import { User } from '../users/entities/user.entity';
import { JwtPayload } from './interfaces/jwt-payload.interface';
import { LoginDto } from './dto/login.dto';
import { CreateUserDto } from '../users/dto/create-user.dto';
import { CreateVendorDto } from '../users/dto/create-vendor.dto';
import { CreateCustomerDto } from '../users/dto/create-customer.dto';
import { Vendor } from '../users/entities/vendor.entity';
import { Customer } from '../users/entities/customer.entity';
import { VendorResponseDto } from '../users/dto/vendor-response.dto';
import { UserRole } from '../../common/interfaces/user-roles.enum';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class AuthService {
  private readonly maxLoginAttempts = 5;
  private readonly loginAttemptWindow = 15 * 60 * 1000; // 15 minutes
  private readonly tokenBlacklist: Set<string> = new Set();

  constructor(
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
    private readonly tenantsService: TenantsService,
    private readonly configService: ConfigService,
  ) {}

  private extractSubdomainFromHost(host: string | undefined): string | null {
    if (!host?.includes('.')) {
      return null;
    }

    const parts = host.split('.');
    // Handle both regular domains and localhost testing
    if (parts.length >= 2 && parts[1].startsWith('localhost')) {
      const subdomain = parts[0];
      if (subdomain && subdomain !== 'www' && subdomain !== 'api') {
        return subdomain;
      }
    }
    // For regular domains (e.g., mystore.example.com)
    else if (parts.length >= 3) {
      const subdomain = parts[0];
      if (subdomain && subdomain !== 'www' && subdomain !== 'api') {
        return subdomain;
      }
    }
    return null;
  }

  async validateUser(
    email: string,
    password: string,
    ip: string,
  ): Promise<Omit<User, 'password'> | null> {
    try {
      const user = await this.usersService.findOneByEmail(email);

      // Check if user is locked
      if (
        user.loginAttempts >= this.maxLoginAttempts &&
        user.lastLoginAttempt &&
        Date.now() - new Date(user.lastLoginAttempt).getTime() <
          this.loginAttemptWindow
      ) {
        const remainingTime = Math.ceil(
          (this.loginAttemptWindow -
            (Date.now() - new Date(user.lastLoginAttempt).getTime())) /
            1000 /
            60,
        );
        throw new ForbiddenException(
          `Account temporarily locked. Please try again in ${remainingTime} minutes`,
        );
      }

      const isPasswordValid = await bcrypt.compare(password, user.password);

      if (!isPasswordValid) {
        await this.usersService.incrementLoginAttempts(user.id);
        const attemptsLeft = this.maxLoginAttempts - (user.loginAttempts + 1);
        if (attemptsLeft > 0) {
          throw new UnauthorizedException(
            `Invalid credentials. ${attemptsLeft} attempts remaining`,
          );
        } else {
          throw new ForbiddenException(
            'Account locked due to too many failed attempts. Please try again later',
          );
        }
      }

      // Reset login attempts on successful login
      await this.usersService.resetLoginAttempts(user.id);

      // Update last login information
      await this.usersService.updateLastLoginInfo(user.id, ip);

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { password: passwordOmitted, ...result } = user;
      return result;
    } catch (error) {
      if (
        error instanceof ForbiddenException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      return null;
    }
  }

  async login(loginDto: LoginDto, req: Request) {
    const ip = req.ip ? String(req.ip) : 'unknown';
    const user = await this.validateUser(loginDto.email, loginDto.password, ip);

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const jti = uuidv4();
    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      role: user.role,
      ip: ip,
      userAgent: req.headers['user-agent'],
      jti: jti,
      iss:
        this.configService.get<string>('JWT_ISSUER') ||
        'multi-tenant-ecommerce-api',
      aud:
        this.configService.get<string>('JWT_AUDIENCE') ||
        'multi-tenant-ecommerce-client',
    };

    const accessToken = this.jwtService.sign(payload, {
      expiresIn: this.configService.get<string>('JWT_EXPIRATION') || '15m',
    });

    const refreshToken = this.jwtService.sign(
      { sub: user.id, jti: uuidv4() },
      {
        expiresIn:
          this.configService.get<string>('JWT_REFRESH_EXPIRATION') || '7d',
        secret: this.configService.get<string>('JWT_REFRESH_SECRET'),
      },
    );

    // Store refresh token and metadata
    await this.usersService.storeRefreshToken(user.id, refreshToken, {
      ip,
      userAgent: req.headers['user-agent'] as string,
      issuedAt: new Date(),
    });

    return {
      access_token: accessToken,
      refresh_token: refreshToken,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
      },
    };
  }

  async loginWithRole(
    loginDto: LoginDto,
    req: Request,
    requiredRole: UserRole,
  ): Promise<{
    access_token: string;
    refresh_token: string;
    user: { id: string; email: string; role: UserRole };
  }> {
    const ip = req.ip ? String(req.ip) : 'unknown';
    const subdomain = this.extractSubdomainFromHost(req.headers.host);

    // Vendor login validation
    if (requiredRole === UserRole.VENDOR) {
      if (!subdomain) {
        throw new UnauthorizedException(
          'Vendors must login through their store portal',
        );
      }
      await this.tenantsService.validateVendorSubdomain(subdomain);
    }

    // Customer login validation
    if (requiredRole === UserRole.CUSTOMER && subdomain) {
      // If logging in through a vendor subdomain, validate it
      await this.tenantsService.validateVendorSubdomain(subdomain);
    }

    // Super Admin login validation
    if (requiredRole === UserRole.SUPER_ADMIN && subdomain) {
      throw new UnauthorizedException(
        'Super admin must login through the main portal',
      );
    }

    const user = await this.validateUser(loginDto.email, loginDto.password, ip);

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    if (user.role !== requiredRole) {
      throw new UnauthorizedException(
        `Invalid login endpoint for ${user.role}. Please use the correct login endpoint`,
      );
    }

    const jti = uuidv4();
    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      role: user.role,
      ip: ip,
      userAgent: req.headers['user-agent'],
      jti: jti,
      iss:
        this.configService.get<string>('JWT_ISSUER') ||
        'multi-tenant-ecommerce-api',
      aud:
        this.configService.get<string>('JWT_AUDIENCE') ||
        'multi-tenant-ecommerce-client',
      // Include the subdomain in the JWT if present
      ...(subdomain && { subdomain }),
    };

    const accessToken = this.jwtService.sign(payload, {
      expiresIn: this.configService.get<string>('JWT_EXPIRATION') || '15m',
    });

    const refreshToken = this.jwtService.sign(
      { sub: user.id, jti: uuidv4() },
      {
        expiresIn:
          this.configService.get<string>('JWT_REFRESH_EXPIRATION') || '7d',
        secret: this.configService.get<string>('JWT_REFRESH_SECRET'),
      },
    );

    // Store refresh token and metadata
    await this.usersService.storeRefreshToken(user.id, refreshToken, {
      ip,
      userAgent: req.headers['user-agent'] as string,
      issuedAt: new Date(),
    });

    return {
      access_token: accessToken,
      refresh_token: refreshToken,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
      },
    };
  }

  async refreshToken(refreshToken: string, req: Request) {
    try {
      const decoded = this.jwtService.verify<{ sub: string; jti: string }>(
        refreshToken,
        {
          secret: this.configService.get<string>('JWT_REFRESH_SECRET'),
        },
      );

      const user = await this.usersService.findOneById(decoded.sub);
      if (!user || user.refreshToken !== refreshToken) {
        throw new UnauthorizedException('Invalid refresh token');
      }

      // Check if the refresh token has been blacklisted
      if (this.tokenBlacklist.has(refreshToken)) {
        throw new UnauthorizedException('Refresh token has been revoked');
      }

      const ip = req.ip ? String(req.ip) : 'unknown';
      const jti = uuidv4();
      const newPayload: JwtPayload = {
        sub: user.id,
        email: user.email,
        role: user.role,
        ip: ip,
        userAgent: req.headers['user-agent'],
        jti: jti,
        iss:
          this.configService.get<string>('JWT_ISSUER') ||
          'multi-tenant-ecommerce-api',
        aud:
          this.configService.get<string>('JWT_AUDIENCE') ||
          'multi-tenant-ecommerce-client',
      };

      const newAccessToken = this.jwtService.sign(newPayload);
      const newRefreshToken = this.jwtService.sign(
        { sub: user.id, jti: uuidv4() },
        {
          expiresIn:
            this.configService.get<string>('JWT_REFRESH_EXPIRATION') || '7d',
          secret: this.configService.get<string>('JWT_REFRESH_SECRET'),
        },
      );

      // Invalidate old refresh token
      this.tokenBlacklist.add(refreshToken);

      // Store new refresh token with metadata
      await this.usersService.storeRefreshToken(user.id, newRefreshToken, {
        ip,
        userAgent: req.headers['user-agent'] as string,
        issuedAt: new Date(),
      });

      return {
        access_token: newAccessToken,
        refresh_token: newRefreshToken,
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  async logout(userId: string): Promise<void> {
    try {
      const user = await this.usersService.findOneById(userId);
      if (user?.refreshToken) {
        // Add the current refresh token to blacklist
        this.tokenBlacklist.add(user.refreshToken);
      }
      await this.usersService.storeRefreshToken(userId, '', {
        ip: 'logout',
        userAgent: 'logout',
        issuedAt: new Date(),
      });
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error(`Error during logout: ${error.message}`);
      } else {
        console.error('Unknown error during logout');
      }
      throw error;
    }
  }

  async registerSuperAdmin(createUserDto: CreateUserDto): Promise<User> {
    if (createUserDto.role !== UserRole.SUPER_ADMIN) {
      throw new BadRequestException(
        'Invalid role for super admin registration',
      );
    }

    return this.usersService.createSuperAdmin(createUserDto);
  }

  async registerVendor(createVendorDto: CreateVendorDto): Promise<VendorResponseDto> {
    const vendor = await this.usersService.createVendor(createVendorDto);

    try {
      if (vendor.subdomain && vendor.databaseName) {
        await this.tenantsService.createTenantDatabase(vendor);
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error(`Error creating tenant database: ${error.message}`);
      } else {
        console.error('Unknown error creating tenant database');
      }
      // Log the error but don't fail the registration
    }

    // Transform to safe response DTO
    const vendorResponse: VendorResponseDto = {
      id: vendor.id,
      storeName: vendor.storeName,
      description: vendor.description,
      logo: vendor.logo,
      contactPhone: vendor.contactPhone,
      contactEmail: vendor.contactEmail,
      address: vendor.address,
      subdomain: vendor.subdomain,
      isActive: vendor.isActive,
      createdAt: vendor.createdAt,
      updatedAt: vendor.updatedAt
    };

    return vendorResponse;
  }

  async registerCustomer(
    createCustomerDto: CreateCustomerDto,
    registrationSource?: string,
  ): Promise<Customer> {
    // If registrationSource contains a subdomain, validate it
    if (registrationSource?.includes('.')) {
      const subdomain = this.extractSubdomainFromHost(registrationSource);
      if (subdomain) {
        await this.tenantsService.validateVendorSubdomain(subdomain);
      }
    }

    return this.usersService.createCustomer(
      createCustomerDto,
      registrationSource,
    );
  }
}
