import { <PERSON><PERSON><PERSON>, Column, PrimaryGenerated<PERSON><PERSON>umn, CreateDateColumn, UpdateDateColumn, OneToOne, JoinColumn, Index } from 'typeorm';
import { User } from './user.entity';
import { ApiProperty } from '@nestjs/swagger';

@Entity('vendors')
export class Vendor {
  // Vendor fields
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  storeName: string;

  @Column({ nullable: true })
  description: string;

  @Column({ nullable: true })
  logo: string;

  @Column({ nullable: true })
  contactPhone: string;

  @Column({ nullable: true })
  contactEmail: string;

  @Column({ nullable: true })
  address: string;

  @OneToOne(() => User)
  @JoinColumn()
  user: User;

  @Column()
  userId: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Tenant fields
  @Column({ unique: true, nullable: true })
  subdomain: string;

  @Column({ nullable: true })
  databaseName: string;

  @Column({ default: true })
  isActive: boolean;
}
