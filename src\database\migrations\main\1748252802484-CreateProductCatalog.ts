import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateProductCatalog1748252802484 implements MigrationInterface {
    name = 'CreateProductCatalog1748252802484'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "product_catalog" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "tenantProductId" uuid NOT NULL, "vendorId" uuid NOT NULL, "tenantSubdomain" character varying NOT NULL, "name" character varying NOT NULL, "description" text, "price" numeric(12,2) NOT NULL, "stock" integer NOT NULL DEFAULT '0', "images" text array NOT NULL DEFAULT '{}', "categoryId" uuid, "subcategoryId" uuid, "isActive" boolean NOT NULL DEFAULT true, "searchVector" tsvector, "categoryPath" text, "stockStatus" character varying, "attributes" jsonb, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "syncedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_43fc6ce23925dbaa92fea160f71" PRIMARY KEY ("id"))`);

        // Performance indexes for common queries
        await queryRunner.query(`CREATE INDEX "IDX_fad528c5eb6cba899c76b1589c" ON "product_catalog" ("vendorId", "isActive") `);
        await queryRunner.query(`CREATE INDEX "IDX_121fa8dcc7d686dd607b3a8abb" ON "product_catalog" ("categoryId", "price") `);
        await queryRunner.query(`CREATE INDEX "IDX_76ae5c122e853bd00e44c0bbd8" ON "product_catalog" ("isActive", "createdAt") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_927dbd285ef75019b80a7c83cf" ON "product_catalog" ("tenantSubdomain", "tenantProductId") `);

        // GIN index for full-text search (provides sub-50ms search performance)
        await queryRunner.query(`CREATE INDEX "IDX_product_catalog_search_vector" ON "product_catalog" USING gin("searchVector")`);

        // Additional performance indexes
        await queryRunner.query(`CREATE INDEX "IDX_product_catalog_stock_status" ON "product_catalog" ("stockStatus", "isActive")`);
        await queryRunner.query(`CREATE INDEX "IDX_product_catalog_price_range" ON "product_catalog" ("price") WHERE "isActive" = true`);
        await queryRunner.query(`CREATE INDEX "IDX_product_catalog_tenant_active" ON "product_catalog" ("tenantSubdomain", "isActive")`);

        // Foreign key constraints
        await queryRunner.query(`ALTER TABLE "product_catalog" ADD CONSTRAINT "FK_e28b4cc5805ab718f2ad1980314" FOREIGN KEY ("categoryId") REFERENCES "categories"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_catalog" ADD CONSTRAINT "FK_bb45e489ad946a05f10c7672271" FOREIGN KEY ("vendorId") REFERENCES "vendors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop foreign key constraints
        await queryRunner.query(`ALTER TABLE "product_catalog" DROP CONSTRAINT "FK_bb45e489ad946a05f10c7672271"`);
        await queryRunner.query(`ALTER TABLE "product_catalog" DROP CONSTRAINT "FK_e28b4cc5805ab718f2ad1980314"`);

        // Drop all indexes
        await queryRunner.query(`DROP INDEX "public"."IDX_product_catalog_tenant_active"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_product_catalog_price_range"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_product_catalog_stock_status"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_product_catalog_search_vector"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_927dbd285ef75019b80a7c83cf"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_76ae5c122e853bd00e44c0bbd8"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_121fa8dcc7d686dd607b3a8abb"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_fad528c5eb6cba899c76b1589c"`);

        // Drop table
        await queryRunner.query(`DROP TABLE "product_catalog"`);
    }

}
