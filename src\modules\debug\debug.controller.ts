import { Controller, Get, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { TenantRequest } from '../../common/interfaces/tenant-request.interface';

@ApiTags('debug')
@Controller('debug')
export class DebugController {
  @ApiOperation({ summary: 'Get tenant information for debugging' })
  @ApiResponse({ status: 200, description: 'Returns tenant information' })
  @Get('tenant')
  getTenantInfo(@Req() req: TenantRequest) {
    return {
      tenantId: req.tenantId || 'No tenant identified',
      host: req.headers.host,
      url: req.url,
      method: req.method,
    };
  }
}
