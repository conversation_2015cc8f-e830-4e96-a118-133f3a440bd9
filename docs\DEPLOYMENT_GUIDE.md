# Multi-Tenant E-commerce API: Deployment Guide

This guide provides instructions for deploying the Multi-Tenant E-commerce API to various environments.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Environment Configuration](#environment-configuration)
- [Database Setup](#database-setup)
- [Deployment Options](#deployment-options)
  - [Local Deployment](#local-deployment)
  - [Docker Deployment](#docker-deployment)
  - [Cloud Deployment](#cloud-deployment)
- [SSL Configuration](#ssl-configuration)
- [Monitoring and Logging](#monitoring-and-logging)
- [Backup and Recovery](#backup-and-recovery)
- [Scaling Considerations](#scaling-considerations)

## Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)
- PostgreSQL (v12 or higher)
- Git

## Environment Configuration

1. Create an environment file based on the example:
   ```bash
   cp .env.example .env.production
   ```

2. Configure the following variables in your `.env.production` file:

   ```
   # Database Configuration
   DB_HOST=your-db-host
   DB_PORT=5432
   DB_USERNAME=your-db-username
   DB_PASSWORD=your-secure-password
   DB_MAIN_DATABASE=ecommerce_main

   # JWT Configuration
   JWT_SECRET=your-very-secure-jwt-secret-key
   JWT_EXPIRATION=1d

   # App Configuration
   PORT=4000
   NODE_ENV=production

   # Super Admin Configuration
   SUPER_ADMIN_EMAIL=<EMAIL>
   SUPER_ADMIN_PASSWORD=SecureAdminPassword123!
   ```

3. Ensure that sensitive information like passwords and JWT secrets are strong and secure.

## Database Setup

1. Create the main database:
   ```bash
   createdb -h your-db-host -U your-db-username ecommerce_main
   ```

2. The application will handle tenant database creation automatically.

3. For production, it's recommended to:
   - Use a dedicated database server
   - Configure proper database backups
   - Set up database replication for high availability
   - Use connection pooling for better performance

## Deployment Options

### Local Deployment

1. Build the application:
   ```bash
   npm run build
   ```

2. Start the application:
   ```bash
   NODE_ENV=production node dist/main
   ```

3. For production-ready process management, use PM2:
   ```bash
   npm install -g pm2
   pm2 start dist/main.js --name "ecommerce-api"
   ```

4. Configure PM2 to start on system boot:
   ```bash
   pm2 startup
   pm2 save
   ```

### Docker Deployment

1. Create a `Dockerfile` in the project root:
   ```dockerfile
   FROM node:16-alpine

   WORKDIR /app

   COPY package*.json ./
   RUN npm ci --only=production

   COPY dist ./dist

   EXPOSE 4000
   CMD ["node", "dist/main"]
   ```

2. Create a `.dockerignore` file:
   ```
   node_modules
   npm-debug.log
   .git
   .env*
   ```

3. Build the Docker image:
   ```bash
   npm run build
   docker build -t multi-tenant-ecommerce-api .
   ```

4. Run the Docker container:
   ```bash
   docker run -p 4000:4000 --env-file .env.production multi-tenant-ecommerce-api
   ```

5. For Docker Compose, create a `docker-compose.yml` file:
   ```yaml
   version: '3'
   services:
     api:
       build: .
       ports:
         - "4000:4000"
       env_file:
         - .env.production
       depends_on:
         - db
     db:
       image: postgres:14
       environment:
         POSTGRES_USER: ${DB_USERNAME}
         POSTGRES_PASSWORD: ${DB_PASSWORD}
         POSTGRES_DB: ${DB_MAIN_DATABASE}
       volumes:
         - postgres_data:/var/lib/postgresql/data
   volumes:
     postgres_data:
   ```

6. Run with Docker Compose:
   ```bash
   docker-compose up -d
   ```

### Cloud Deployment

#### AWS Elastic Beanstalk

1. Install the EB CLI:
   ```bash
   pip install awsebcli
   ```

2. Initialize EB CLI:
   ```bash
   eb init
   ```

3. Create an environment:
   ```bash
   eb create production
   ```

4. Deploy:
   ```bash
   eb deploy
   ```

#### Heroku

1. Install the Heroku CLI:
   ```bash
   npm install -g heroku
   ```

2. Login to Heroku:
   ```bash
   heroku login
   ```

3. Create a Heroku app:
   ```bash
   heroku create your-app-name
   ```

4. Add PostgreSQL:
   ```bash
   heroku addons:create heroku-postgresql:hobby-dev
   ```

5. Configure environment variables:
   ```bash
   heroku config:set JWT_SECRET=your-jwt-secret
   heroku config:set SUPER_ADMIN_EMAIL=<EMAIL>
   heroku config:set SUPER_ADMIN_PASSWORD=SecureAdminPassword123!
   ```

6. Deploy:
   ```bash
   git push heroku main
   ```

## SSL Configuration

For production deployments, always use HTTPS:

1. Obtain SSL certificates (Let's Encrypt, commercial CA, etc.)

2. For Nginx as a reverse proxy:
   ```nginx
   server {
       listen 80;
       server_name api.yourdomain.com;
       return 301 https://$host$request_uri;
   }

   server {
       listen 443 ssl;
       server_name api.yourdomain.com;

       ssl_certificate /path/to/fullchain.pem;
       ssl_certificate_key /path/to/privkey.pem;

       location / {
           proxy_pass http://localhost:4000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

## Monitoring and Logging

1. Configure application logging:
   - Use a logging library like Winston
   - Send logs to a centralized logging service

2. Set up monitoring:
   - Use PM2 monitoring: `pm2 monit`
   - Consider services like New Relic, Datadog, or Prometheus + Grafana

3. Set up alerts for:
   - High CPU/memory usage
   - API response time degradation
   - Error rate spikes
   - Database connection issues

## Backup and Recovery

1. Database Backups:
   ```bash
   # Backup main database
   pg_dump -h your-db-host -U your-db-username ecommerce_main > backup_main_$(date +%Y%m%d).sql

   # Backup tenant databases (script example)
   for DB in $(psql -h your-db-host -U your-db-username -c "SELECT datname FROM pg_database WHERE datname LIKE 'tenant_%'" -t); do
     pg_dump -h your-db-host -U your-db-username $DB > backup_${DB}_$(date +%Y%m%d).sql
   done
   ```

2. Automate backups with cron jobs:
   ```
   0 2 * * * /path/to/backup_script.sh
   ```

3. Store backups securely:
   - Use encrypted storage
   - Keep offsite backups
   - Test restoration procedures regularly

## Scaling Considerations

1. **Horizontal Scaling**:
   - Deploy multiple API instances behind a load balancer
   - Ensure the application is stateless (JWT helps with this)

2. **Database Scaling**:
   - Use connection pooling
   - Consider read replicas for read-heavy workloads
   - Implement database sharding for very large deployments

3. **Caching**:
   - Implement Redis for caching frequently accessed data
   - Cache tenant connections to reduce database connection overhead

4. **CDN Integration**:
   - Use a CDN for static assets
   - Configure proper cache headers

5. **Microservices Evolution**:
   - Consider breaking down the monolith into microservices as the application grows
   - Use message queues for asynchronous processing of heavy tasks
